/**
 * useProgressEstimation Hook
 * Provides time estimation and completion prediction for workflows
 */

import { useState, useEffect, useMemo } from 'react';
import { StepProgressInfo } from './useWorkflowProgress';

export interface TimeEstimation {
  totalEstimated: number;
  elapsed: number;
  remaining: number;
  completionTime: Date;
  confidence: number;
  accuracy: number;
}

export interface StepEstimation {
  stepId: string;
  estimatedDuration: number;
  actualDuration?: number;
  remainingTime: number;
  confidence: number;
  startTime?: Date;
  estimatedCompletion: Date;
}

export interface EstimationMetrics {
  averageStepDuration: number;
  fastestStep: number;
  slowestStep: number;
  variability: number;
  predictability: number;
}

export interface UseProgressEstimationOptions {
  executionId?: string;
  includeHistorical?: boolean;
  learningEnabled?: boolean;
  confidenceThreshold?: number;
  updateInterval?: number;
}

export interface UseProgressEstimationReturn {
  estimation: TimeEstimation | null;
  stepEstimations: StepEstimation[];
  metrics: EstimationMetrics;
  isLoading: boolean;
  error: string | null;
  updateEstimation: (stepId: string, duration: number) => void;
  recalculate: () => void;
  getStepPrediction: (stepId: string) => StepEstimation | null;
}

export function useProgressEstimation(
  steps: StepProgressInfo[],
  startTime?: string,
  options: UseProgressEstimationOptions = {}
): UseProgressEstimationReturn {
  const {
    executionId,
    includeHistorical = true,
    learningEnabled = true,
    confidenceThreshold = 0.7,
    updateInterval = 10000
  } = options;

  const [historicalData, setHistoricalData] = useState<any[]>([]);
  const [customEstimates, setCustomEstimates] = useState<Record<string, number>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time periodically
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, updateInterval);

    return () => clearInterval(timer);
  }, [updateInterval]);

  // Fetch historical data for learning
  const fetchHistoricalData = async () => {
    if (!includeHistorical) return;

    try {
      setIsLoading(true);
      const response = await fetch('/api/workflow/history?limit=50&includeSteps=true');
      if (response.ok) {
        const data = await response.json();
        setHistoricalData(data.executions || []);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch historical data');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate base estimates from historical data
  const calculateBaseEstimates = (): Record<string, { duration: number; confidence: number }> => {
    const estimates: Record<string, { durations: number[]; confidence: number }> = {};

    // Collect historical durations for each step type
    historicalData.forEach(execution => {
      if (execution.stepResults) {
        execution.stepResults.forEach((step: any) => {
          if (step.duration && step.status === 'completed') {
            const key = step.stepName || step.stepId;
            if (!estimates[key]) {
              estimates[key] = { durations: [], confidence: 0 };
            }
            estimates[key].durations.push(step.duration);
          }
        });
      }
    });

    // Calculate averages and confidence
    const baseEstimates: Record<string, { duration: number; confidence: number }> = {};
    Object.entries(estimates).forEach(([key, data]) => {
      if (data.durations.length > 0) {
        const avg = data.durations.reduce((sum, d) => sum + d, 0) / data.durations.length;
        const variance = data.durations.reduce((sum, d) => sum + Math.pow(d - avg, 2), 0) / data.durations.length;
        const stdDev = Math.sqrt(variance);
        const confidence = Math.max(0.1, Math.min(0.9, 1 - (stdDev / avg)));

        baseEstimates[key] = {
          duration: avg,
          confidence: confidence * (Math.min(data.durations.length, 10) / 10) // More data = higher confidence
        };
      }
    });

    return baseEstimates;
  };

  // Calculate step estimations
  const stepEstimations = useMemo((): StepEstimation[] => {
    const baseEstimates = calculateBaseEstimates();
    const start = startTime ? new Date(startTime) : new Date();

    return steps.map((step, index) => {
      const stepKey = step.stepName || step.stepId;
      const baseEstimate = baseEstimates[stepKey];
      const customEstimate = customEstimates[step.stepId];

      // Use custom estimate if available, otherwise use base estimate or default
      let estimatedDuration = customEstimate || baseEstimate?.duration || 300000; // 5 minutes default
      let confidence = baseEstimate?.confidence || 0.3;

      // Adjust based on step complexity or other factors
      if (step.stepId.includes('review') || step.stepId.includes('approval')) {
        estimatedDuration *= 2; // Reviews typically take longer
        confidence *= 0.8; // Less predictable
      }

      // Calculate timing
      const actualStart = step.startedAt ? new Date(step.startedAt) : null;
      const actualEnd = step.completedAt ? new Date(step.completedAt) : null;
      const actualDuration = actualStart && actualEnd ? actualEnd.getTime() - actualStart.getTime() : undefined;

      // Estimate start time based on previous steps
      let estimatedStart = start;
      for (let i = 0; i < index; i++) {
        const prevStep = steps[i];
        const prevEstimation = baseEstimates[prevStep.stepName || prevStep.stepId];
        estimatedStart = new Date(estimatedStart.getTime() + (prevEstimation?.duration || 300000));
      }

      const estimatedCompletion = new Date(estimatedStart.getTime() + estimatedDuration);

      // Calculate remaining time
      let remainingTime = estimatedDuration;
      if (actualStart) {
        if (actualEnd) {
          remainingTime = 0; // Step completed
        } else {
          // Step in progress
          const elapsed = currentTime.getTime() - actualStart.getTime();
          remainingTime = Math.max(0, estimatedDuration - elapsed);
        }
      }

      return {
        stepId: step.stepId,
        estimatedDuration,
        actualDuration,
        remainingTime,
        confidence,
        startTime: actualStart || estimatedStart,
        estimatedCompletion
      };
    });
  }, [steps, startTime, currentTime, historicalData, customEstimates]);

  // Calculate overall estimation
  const estimation = useMemo((): TimeEstimation | null => {
    if (!startTime || steps.length === 0) return null;

    const start = new Date(startTime);
    const elapsed = currentTime.getTime() - start.getTime();

    const totalEstimated = stepEstimations.reduce((sum, step) => sum + step.estimatedDuration, 0);
    const totalRemaining = stepEstimations.reduce((sum, step) => sum + step.remainingTime, 0);
    const completionTime = new Date(currentTime.getTime() + totalRemaining);

    // Calculate confidence based on step confidences and completion status
    const completedSteps = steps.filter(s => s.status === 'completed').length;
    const totalSteps = steps.length;
    const completionRatio = totalSteps > 0 ? completedSteps / totalSteps : 0;

    const avgStepConfidence = stepEstimations.reduce((sum, step) => sum + step.confidence, 0) / stepEstimations.length;
    const confidence = Math.min(0.9, avgStepConfidence * (0.5 + completionRatio * 0.5));

    // Calculate accuracy based on actual vs estimated for completed steps
    const completedEstimations = stepEstimations.filter(est => {
      const step = steps.find(s => s.stepId === est.stepId);
      return step && step.status === 'completed' && est.actualDuration;
    });

    let accuracy = 0.5; // Default accuracy
    if (completedEstimations.length > 0) {
      const accuracySum = completedEstimations.reduce((sum, est) => {
        const ratio = Math.min(est.actualDuration!, est.estimatedDuration) / Math.max(est.actualDuration!, est.estimatedDuration);
        return sum + ratio;
      }, 0);
      accuracy = accuracySum / completedEstimations.length;
    }

    return {
      totalEstimated,
      elapsed,
      remaining: totalRemaining,
      completionTime,
      confidence,
      accuracy
    };
  }, [steps, startTime, currentTime, stepEstimations]);

  // Calculate metrics
  const metrics = useMemo((): EstimationMetrics => {
    const completedSteps = stepEstimations.filter(est => est.actualDuration);
    
    if (completedSteps.length === 0) {
      return {
        averageStepDuration: 0,
        fastestStep: 0,
        slowestStep: 0,
        variability: 0,
        predictability: 0
      };
    }

    const durations = completedSteps.map(step => step.actualDuration!);
    const averageStepDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length;
    const fastestStep = Math.min(...durations);
    const slowestStep = Math.max(...durations);

    // Calculate variability (coefficient of variation)
    const variance = durations.reduce((sum, d) => sum + Math.pow(d - averageStepDuration, 2), 0) / durations.length;
    const stdDev = Math.sqrt(variance);
    const variability = averageStepDuration > 0 ? stdDev / averageStepDuration : 0;

    // Calculate predictability (how close estimates were to actual)
    const predictions = completedSteps.map(step => {
      const ratio = Math.min(step.actualDuration!, step.estimatedDuration) / Math.max(step.actualDuration!, step.estimatedDuration);
      return ratio;
    });
    const predictability = predictions.reduce((sum, p) => sum + p, 0) / predictions.length;

    return {
      averageStepDuration,
      fastestStep,
      slowestStep,
      variability,
      predictability
    };
  }, [stepEstimations]);

  // Update estimation for a specific step
  const updateEstimation = (stepId: string, duration: number) => {
    setCustomEstimates(prev => ({
      ...prev,
      [stepId]: duration
    }));
  };

  // Recalculate estimations
  const recalculate = () => {
    if (learningEnabled) {
      fetchHistoricalData();
    }
  };

  // Get prediction for a specific step
  const getStepPrediction = (stepId: string): StepEstimation | null => {
    return stepEstimations.find(est => est.stepId === stepId) || null;
  };

  // Fetch historical data on mount
  useEffect(() => {
    if (includeHistorical && learningEnabled) {
      fetchHistoricalData();
    }
  }, [includeHistorical, learningEnabled]);

  return {
    estimation,
    stepEstimations,
    metrics,
    isLoading,
    error,
    updateEstimation,
    recalculate,
    getStepPrediction
  };
}
