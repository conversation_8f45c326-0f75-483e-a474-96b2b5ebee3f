/**
 * useCostTracking Hook
 * Real-time AI cost tracking and budget management
 */

import { useState, useEffect, useMemo, useCallback } from 'react';

export interface CostMetrics {
  totalCost: number;
  costByStep: Record<string, number>;
  costByAgent: Record<string, number>;
  costByModel: Record<string, number>;
  costByProvider: Record<string, number>;
  tokenUsage: TokenUsage;
  efficiency: CostEfficiency;
  trends: CostTrend[];
}

export interface TokenUsage {
  totalTokens: number;
  inputTokens: number;
  outputTokens: number;
  tokensByStep: Record<string, number>;
  tokensByModel: Record<string, number>;
  averageTokensPerRequest: number;
}

export interface CostEfficiency {
  costPerToken: number;
  tokensPerDollar: number;
  costPerStep: number;
  costPerMinute: number;
  efficiencyScore: number; // 0-100
}

export interface CostTrend {
  timestamp: string;
  cost: number;
  tokens: number;
  stepId?: string;
  model?: string;
}

export interface BudgetStatus {
  limit: number;
  used: number;
  remaining: number;
  percentage: number;
  status: 'safe' | 'warning' | 'danger' | 'exceeded';
  alertThreshold: number;
  projectedTotal?: number;
  projectedOverrun?: number;
}

export interface CostAlert {
  id: string;
  type: 'budget_warning' | 'budget_exceeded' | 'high_cost_step' | 'efficiency_drop';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  data?: any;
}

export interface UseCostTrackingOptions {
  executionId?: string;
  budget?: number;
  alertThreshold?: number;
  updateInterval?: number;
  enableAlerts?: boolean;
  trackEfficiency?: boolean;
}

export interface UseCostTrackingReturn {
  metrics: CostMetrics;
  budget: BudgetStatus | null;
  alerts: CostAlert[];
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  setBudget: (amount: number, threshold?: number) => void;
  dismissAlert: (alertId: string) => void;
  exportCostData: (format: 'csv' | 'json') => Promise<void>;
  getCostProjection: (timeHorizon: number) => number;
  getStepCostAnalysis: (stepId: string) => StepCostAnalysis | null;
}

export interface StepCostAnalysis {
  stepId: string;
  totalCost: number;
  tokenUsage: number;
  efficiency: number;
  costBreakdown: {
    model: string;
    cost: number;
    tokens: number;
  }[];
  comparison: {
    averageCost: number;
    percentile: number;
    isExpensive: boolean;
  };
}

export function useCostTracking(
  executionId?: string,
  options: UseCostTrackingOptions = {}
): UseCostTrackingReturn {
  const {
    budget: budgetLimit,
    alertThreshold = 0.8,
    updateInterval = 5000,
    enableAlerts = true,
    trackEfficiency = true
  } = options;

  const [metrics, setMetrics] = useState<CostMetrics>({
    totalCost: 0,
    costByStep: {},
    costByAgent: {},
    costByModel: {},
    costByProvider: {},
    tokenUsage: {
      totalTokens: 0,
      inputTokens: 0,
      outputTokens: 0,
      tokensByStep: {},
      tokensByModel: {},
      averageTokensPerRequest: 0
    },
    efficiency: {
      costPerToken: 0,
      tokensPerDollar: 0,
      costPerStep: 0,
      costPerMinute: 0,
      efficiencyScore: 0
    },
    trends: []
  });

  const [budget, setBudgetState] = useState<BudgetStatus | null>(null);
  const [alerts, setAlerts] = useState<CostAlert[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [historicalData, setHistoricalData] = useState<any[]>([]);

  // Fetch cost data from API
  const fetchCostData = async () => {
    if (!executionId) return;

    try {
      const response = await fetch(`/api/workflow/costs/${executionId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch cost data');
      }

      const data = await response.json();
      
      // Process and update metrics
      const newMetrics: CostMetrics = {
        totalCost: data.totalCost || 0,
        costByStep: data.costByStep || {},
        costByAgent: data.costByAgent || {},
        costByModel: data.costByModel || {},
        costByProvider: data.costByProvider || {},
        tokenUsage: {
          totalTokens: data.tokenUsage?.totalTokens || 0,
          inputTokens: data.tokenUsage?.inputTokens || 0,
          outputTokens: data.tokenUsage?.outputTokens || 0,
          tokensByStep: data.tokenUsage?.tokensByStep || {},
          tokensByModel: data.tokenUsage?.tokensByModel || {},
          averageTokensPerRequest: data.tokenUsage?.averageTokensPerRequest || 0
        },
        efficiency: calculateEfficiency(data),
        trends: data.trends || []
      };

      setMetrics(newMetrics);
      
      // Update budget status
      if (budgetLimit) {
        updateBudgetStatus(newMetrics.totalCost);
      }

      // Check for alerts
      if (enableAlerts) {
        checkForAlerts(newMetrics);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    }
  };

  // Calculate efficiency metrics
  const calculateEfficiency = (data: any): CostEfficiency => {
    const totalCost = data.totalCost || 0;
    const totalTokens = data.tokenUsage?.totalTokens || 0;
    const totalSteps = Object.keys(data.costByStep || {}).length;
    const totalTime = data.totalTime || 1; // in minutes

    const costPerToken = totalTokens > 0 ? totalCost / totalTokens : 0;
    const tokensPerDollar = totalCost > 0 ? totalTokens / totalCost : 0;
    const costPerStep = totalSteps > 0 ? totalCost / totalSteps : 0;
    const costPerMinute = totalTime > 0 ? totalCost / totalTime : 0;

    // Calculate efficiency score (0-100)
    // Based on cost per token compared to industry averages
    const industryAvgCostPerToken = 0.00002; // $0.00002 per token (rough estimate)
    const efficiencyRatio = industryAvgCostPerToken / (costPerToken || industryAvgCostPerToken);
    const efficiencyScore = Math.min(100, Math.max(0, efficiencyRatio * 100));

    return {
      costPerToken,
      tokensPerDollar,
      costPerStep,
      costPerMinute,
      efficiencyScore
    };
  };

  // Update budget status
  const updateBudgetStatus = (currentCost: number) => {
    if (!budgetLimit) return;

    const used = currentCost;
    const remaining = Math.max(0, budgetLimit - used);
    const percentage = (used / budgetLimit) * 100;

    let status: BudgetStatus['status'] = 'safe';
    if (percentage >= 100) status = 'exceeded';
    else if (percentage >= 90) status = 'danger';
    else if (percentage >= alertThreshold * 100) status = 'warning';

    // Calculate projection based on current trend
    const recentTrends = metrics.trends.slice(-5);
    let projectedTotal = used;
    if (recentTrends.length >= 2) {
      const avgCostIncrease = recentTrends.reduce((sum, trend, index) => {
        if (index === 0) return sum;
        return sum + (trend.cost - recentTrends[index - 1].cost);
      }, 0) / (recentTrends.length - 1);
      
      projectedTotal = used + (avgCostIncrease * 10); // Project 10 intervals ahead
    }

    setBudgetState({
      limit: budgetLimit,
      used,
      remaining,
      percentage,
      status,
      alertThreshold,
      projectedTotal,
      projectedOverrun: Math.max(0, projectedTotal - budgetLimit)
    });
  };

  // Check for cost alerts
  const checkForAlerts = (currentMetrics: CostMetrics) => {
    const newAlerts: CostAlert[] = [];

    // Budget alerts
    if (budget) {
      if (budget.status === 'exceeded' && !alerts.some(a => a.type === 'budget_exceeded')) {
        newAlerts.push({
          id: `budget_exceeded_${Date.now()}`,
          type: 'budget_exceeded',
          message: `Budget exceeded! Current cost: $${currentMetrics.totalCost.toFixed(4)}, Budget: $${budget.limit.toFixed(4)}`,
          severity: 'critical',
          timestamp: new Date().toISOString()
        });
      } else if (budget.status === 'warning' && !alerts.some(a => a.type === 'budget_warning')) {
        newAlerts.push({
          id: `budget_warning_${Date.now()}`,
          type: 'budget_warning',
          message: `Budget alert: ${budget.percentage.toFixed(1)}% of budget used`,
          severity: 'medium',
          timestamp: new Date().toISOString()
        });
      }
    }

    // High cost step alerts
    const avgStepCost = currentMetrics.totalCost / Math.max(1, Object.keys(currentMetrics.costByStep).length);
    Object.entries(currentMetrics.costByStep).forEach(([stepId, cost]) => {
      if (cost > avgStepCost * 3) { // 3x average
        const existingAlert = alerts.find(a => a.type === 'high_cost_step' && a.data?.stepId === stepId);
        if (!existingAlert) {
          newAlerts.push({
            id: `high_cost_step_${stepId}_${Date.now()}`,
            type: 'high_cost_step',
            message: `High cost detected for step ${stepId}: $${cost.toFixed(4)}`,
            severity: 'medium',
            timestamp: new Date().toISOString(),
            data: { stepId, cost }
          });
        }
      }
    });

    // Efficiency drop alerts
    if (trackEfficiency && currentMetrics.efficiency.efficiencyScore < 50) {
      const existingAlert = alerts.find(a => a.type === 'efficiency_drop');
      if (!existingAlert) {
        newAlerts.push({
          id: `efficiency_drop_${Date.now()}`,
          type: 'efficiency_drop',
          message: `Low efficiency detected: ${currentMetrics.efficiency.efficiencyScore.toFixed(1)}% efficiency score`,
          severity: 'low',
          timestamp: new Date().toISOString(),
          data: { efficiencyScore: currentMetrics.efficiency.efficiencyScore }
        });
      }
    }

    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev, ...newAlerts]);
    }
  };

  // Refresh data
  const refresh = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await fetchCostData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh cost data');
    } finally {
      setIsLoading(false);
    }
  };

  // Set budget
  const setBudget = (amount: number, threshold = alertThreshold) => {
    setBudgetState(prev => prev ? { ...prev, limit: amount, alertThreshold: threshold } : {
      limit: amount,
      used: metrics.totalCost,
      remaining: Math.max(0, amount - metrics.totalCost),
      percentage: (metrics.totalCost / amount) * 100,
      status: 'safe',
      alertThreshold: threshold
    });
  };

  // Dismiss alert
  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  // Export cost data
  const exportCostData = async (format: 'csv' | 'json') => {
    try {
      const response = await fetch(`/api/workflow/costs/${executionId}/export?format=${format}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `cost-data-${executionId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (err) {
      console.error('Failed to export cost data:', err);
    }
  };

  // Get cost projection
  const getCostProjection = (timeHorizon: number): number => {
    if (metrics.trends.length < 2) return metrics.totalCost;

    const recentTrends = metrics.trends.slice(-Math.min(10, metrics.trends.length));
    const avgCostIncrease = recentTrends.reduce((sum, trend, index) => {
      if (index === 0) return sum;
      return sum + (trend.cost - recentTrends[index - 1].cost);
    }, 0) / (recentTrends.length - 1);

    return metrics.totalCost + (avgCostIncrease * timeHorizon);
  };

  // Get step cost analysis
  const getStepCostAnalysis = (stepId: string): StepCostAnalysis | null => {
    const stepCost = metrics.costByStep[stepId];
    if (!stepCost) return null;

    const stepTokens = metrics.tokenUsage.tokensByStep[stepId] || 0;
    const efficiency = stepTokens > 0 ? stepTokens / stepCost : 0;

    // Calculate comparison with other steps
    const allStepCosts = Object.values(metrics.costByStep);
    const averageCost = allStepCosts.reduce((sum, cost) => sum + cost, 0) / allStepCosts.length;
    const sortedCosts = allStepCosts.sort((a, b) => a - b);
    const percentile = (sortedCosts.indexOf(stepCost) / sortedCosts.length) * 100;

    return {
      stepId,
      totalCost: stepCost,
      tokenUsage: stepTokens,
      efficiency,
      costBreakdown: [], // Would need more detailed data from API
      comparison: {
        averageCost,
        percentile,
        isExpensive: stepCost > averageCost * 1.5
      }
    };
  };

  // Auto-refresh data
  useEffect(() => {
    if (executionId) {
      refresh();
      
      const interval = setInterval(refresh, updateInterval);
      return () => clearInterval(interval);
    }
  }, [executionId, updateInterval]);

  // Initialize budget if provided
  useEffect(() => {
    if (budgetLimit && !budget) {
      setBudget(budgetLimit, alertThreshold);
    }
  }, [budgetLimit, alertThreshold]);

  return {
    metrics,
    budget,
    alerts,
    isLoading,
    error,
    refresh,
    setBudget,
    dismissAlert,
    exportCostData,
    getCostProjection,
    getStepCostAnalysis
  };
}
