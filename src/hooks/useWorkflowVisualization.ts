/**
 * useWorkflowVisualization Hook
 * Provides data and utilities for workflow visualization components
 */

import { useState, useEffect, useMemo } from 'react';
import { StepProgressInfo, WorkflowEvent } from './useWorkflowProgress';

export interface VisualizationData {
  steps: EnhancedStepInfo[];
  dependencies: Record<string, string[]>;
  timeline: TimelineData;
  performance: PerformanceData;
  costs: CostData;
}

export interface EnhancedStepInfo extends StepProgressInfo {
  level: number;
  dependencies: string[];
  dependents: string[];
  estimatedDuration?: number;
  actualDuration?: number;
  efficiency?: number;
  cost?: number;
  tokens?: number;
}

export interface TimelineData {
  startTime: Date;
  endTime?: Date;
  totalDuration: number;
  estimatedTotalDuration: number;
  milestones: Milestone[];
}

export interface Milestone {
  id: string;
  name: string;
  timestamp: Date;
  type: 'start' | 'checkpoint' | 'review' | 'completion' | 'error';
}

export interface PerformanceData {
  overallEfficiency: number;
  bottlenecks: string[];
  criticalPath: string[];
  averageStepDuration: number;
  successRate: number;
}

export interface CostData {
  totalCost: number;
  costByStep: Record<string, number>;
  costByModel: Record<string, number>;
  tokenUsage: {
    total: number;
    byStep: Record<string, number>;
  };
  efficiency: number;
}

export interface UseWorkflowVisualizationOptions {
  executionId?: string;
  includeHistorical?: boolean;
  refreshInterval?: number;
  enableRealTime?: boolean;
}

export interface UseWorkflowVisualizationReturn {
  data: VisualizationData | null;
  isLoading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  updateDependencies: (dependencies: Record<string, string[]>) => void;
  calculateCriticalPath: () => string[];
  getStepMetrics: (stepId: string) => StepMetrics | null;
}

export interface StepMetrics {
  duration: number;
  cost: number;
  tokens: number;
  efficiency: number;
  status: string;
  dependencies: string[];
  dependents: string[];
}

export function useWorkflowVisualization(
  steps: StepProgressInfo[],
  events: WorkflowEvent[],
  options: UseWorkflowVisualizationOptions = {}
): UseWorkflowVisualizationReturn {
  const {
    executionId,
    includeHistorical = false,
    refreshInterval = 30000,
    enableRealTime = true
  } = options;

  const [costData, setCostData] = useState<Record<string, any>>({});
  const [dependencies, setDependencies] = useState<Record<string, string[]>>({});
  const [historicalData, setHistoricalData] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch cost data
  const fetchCostData = async () => {
    if (!executionId) return;

    try {
      const response = await fetch(`/api/workflow/costs/${executionId}`);
      if (response.ok) {
        const data = await response.json();
        setCostData(data);
      }
    } catch (err) {
      console.error('Failed to fetch cost data:', err);
    }
  };

  // Fetch dependencies
  const fetchDependencies = async () => {
    if (!executionId) return;

    try {
      const response = await fetch(`/api/workflow/dependencies/${executionId}`);
      if (response.ok) {
        const data = await response.json();
        setDependencies(data.dependencies || {});
      }
    } catch (err) {
      console.error('Failed to fetch dependencies:', err);
    }
  };

  // Fetch historical data
  const fetchHistoricalData = async () => {
    if (!includeHistorical) return;

    try {
      const response = await fetch(`/api/workflow/history?limit=100`);
      if (response.ok) {
        const data = await response.json();
        setHistoricalData(data.executions || []);
      }
    } catch (err) {
      console.error('Failed to fetch historical data:', err);
    }
  };

  // Refresh all data
  const refresh = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchCostData(),
        fetchDependencies(),
        fetchHistoricalData()
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh data');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate step levels based on dependencies
  const calculateStepLevels = (deps: Record<string, string[]>) => {
    const levels: Record<string, number> = {};
    
    const calculateLevel = (stepId: string, visited = new Set<string>()): number => {
      if (visited.has(stepId)) return 0; // Circular dependency protection
      if (levels[stepId] !== undefined) return levels[stepId];

      visited.add(stepId);
      const stepDeps = deps[stepId] || [];
      
      if (stepDeps.length === 0) {
        levels[stepId] = 0;
      } else {
        const maxDepLevel = Math.max(...stepDeps.map(dep => calculateLevel(dep, visited)));
        levels[stepId] = maxDepLevel + 1;
      }

      visited.delete(stepId);
      return levels[stepId];
    };

    steps.forEach(step => calculateLevel(step.stepId));
    return levels;
  };

  // Calculate critical path
  const calculateCriticalPath = (): string[] => {
    const levels = calculateStepLevels(dependencies);
    const visited = new Set<string>();
    
    const findLongestPath = (stepId: string, currentPath: string[]): string[] => {
      if (visited.has(stepId)) return currentPath;
      
      visited.add(stepId);
      const newPath = [...currentPath, stepId];
      
      const dependents = Object.entries(dependencies)
        .filter(([_, deps]) => deps.includes(stepId))
        .map(([id]) => id);
      
      if (dependents.length === 0) {
        return newPath;
      }

      let longestPath = newPath;
      dependents.forEach(dependent => {
        const depPath = findLongestPath(dependent, newPath);
        if (depPath.length > longestPath.length) {
          longestPath = depPath;
        }
      });

      return longestPath;
    };

    // Find root steps (no dependencies)
    const rootSteps = steps.filter(step => 
      !dependencies[step.stepId] || dependencies[step.stepId].length === 0
    );

    let longestOverallPath: string[] = [];
    rootSteps.forEach(root => {
      visited.clear();
      const path = findLongestPath(root.stepId, []);
      if (path.length > longestOverallPath.length) {
        longestOverallPath = path;
      }
    });

    return longestOverallPath;
  };

  // Get metrics for a specific step
  const getStepMetrics = (stepId: string): StepMetrics | null => {
    const step = steps.find(s => s.stepId === stepId);
    if (!step) return null;

    const stepCost = costData.costByStep?.[stepId] || 0;
    const stepTokens = costData.tokenUsage?.byStep?.[stepId] || 0;
    const stepDeps = dependencies[stepId] || [];
    const stepDependents = Object.entries(dependencies)
      .filter(([_, deps]) => deps.includes(stepId))
      .map(([id]) => id);

    return {
      duration: step.duration || 0,
      cost: stepCost,
      tokens: stepTokens,
      efficiency: stepCost > 0 && stepTokens > 0 ? (stepTokens / stepCost) : 0,
      status: step.status,
      dependencies: stepDeps,
      dependents: stepDependents
    };
  };

  // Process visualization data
  const data = useMemo((): VisualizationData | null => {
    if (steps.length === 0) return null;

    const levels = calculateStepLevels(dependencies);
    const criticalPath = calculateCriticalPath();

    // Enhanced steps with additional data
    const enhancedSteps: EnhancedStepInfo[] = steps.map(step => {
      const stepDeps = dependencies[step.stepId] || [];
      const stepDependents = Object.entries(dependencies)
        .filter(([_, deps]) => deps.includes(step.stepId))
        .map(([id]) => id);

      const stepCost = costData.costByStep?.[step.stepId] || 0;
      const stepTokens = costData.tokenUsage?.byStep?.[step.stepId] || 0;

      return {
        ...step,
        level: levels[step.stepId] || 0,
        dependencies: stepDeps,
        dependents: stepDependents,
        cost: stepCost,
        tokens: stepTokens,
        efficiency: stepCost > 0 && stepTokens > 0 ? (stepTokens / stepCost) : 0
      };
    });

    // Timeline data
    const startTime = steps.find(s => s.startedAt) 
      ? new Date(Math.min(...steps.filter(s => s.startedAt).map(s => new Date(s.startedAt!).getTime())))
      : new Date();
    
    const endTime = steps.every(s => s.completedAt)
      ? new Date(Math.max(...steps.filter(s => s.completedAt).map(s => new Date(s.completedAt!).getTime())))
      : undefined;

    const totalDuration = endTime ? endTime.getTime() - startTime.getTime() : 0;

    // Create milestones from events
    const milestones: Milestone[] = events
      .filter(event => ['workflow_started', 'workflow_completed', 'workflow_failed', 'review_requested'].includes(event.type))
      .map(event => ({
        id: event.id,
        name: event.type.replace('_', ' '),
        timestamp: new Date(event.timestamp),
        type: event.type.includes('start') ? 'start' :
              event.type.includes('completed') ? 'completion' :
              event.type.includes('failed') ? 'error' :
              event.type.includes('review') ? 'review' : 'checkpoint'
      }));

    // Performance data
    const completedSteps = steps.filter(s => s.status === 'completed');
    const failedSteps = steps.filter(s => s.status === 'failed');
    const averageStepDuration = completedSteps.length > 0
      ? completedSteps.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSteps.length
      : 0;

    const bottlenecks = enhancedSteps
      .filter(s => s.duration && s.duration > averageStepDuration * 1.5)
      .map(s => s.stepId);

    const timeline: TimelineData = {
      startTime,
      endTime,
      totalDuration,
      estimatedTotalDuration: enhancedSteps.reduce((sum, s) => sum + (s.estimatedDuration || 0), 0),
      milestones
    };

    const performance: PerformanceData = {
      overallEfficiency: completedSteps.length / steps.length * 100,
      bottlenecks,
      criticalPath,
      averageStepDuration,
      successRate: steps.length > 0 ? (completedSteps.length / steps.length) * 100 : 0
    };

    const costs: CostData = {
      totalCost: costData.totalCost || 0,
      costByStep: costData.costByStep || {},
      costByModel: costData.costByModel || {},
      tokenUsage: {
        total: costData.tokenUsage?.totalTokens || 0,
        byStep: costData.tokenUsage?.byStep || {}
      },
      efficiency: costData.efficiency || 0
    };

    return {
      steps: enhancedSteps,
      dependencies,
      timeline,
      performance,
      costs
    };
  }, [steps, events, dependencies, costData]);

  // Auto-refresh data
  useEffect(() => {
    if (enableRealTime && executionId) {
      refresh();
      
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [executionId, enableRealTime, refreshInterval]);

  // Update dependencies
  const updateDependencies = (newDependencies: Record<string, string[]>) => {
    setDependencies(newDependencies);
  };

  return {
    data,
    isLoading,
    error,
    refresh,
    updateDependencies,
    calculateCriticalPath,
    getStepMetrics
  };
}
