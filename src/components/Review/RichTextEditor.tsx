/**
 * Rich Text Editor Component
 * 
 * Advanced text editor with formatting, AI assistance, and inline commenting
 */

'use client';

import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  Bold, 
  Italic, 
  Underline,
  List,
  ListOrdered,
  Quote,
  Link,
  Image,
  Undo,
  Redo,
  Save,
  Bot,
  MessageSquare,
  Eye,
  Type
} from 'lucide-react';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  onTextSelection?: (selection: {start: number; end: number; text: string}) => void;
  aiAssistanceEnabled?: boolean;
  comments?: Array<{
    id: string;
    position?: {start: number; end: number};
    content: string;
    resolved: boolean;
  }>;
  className?: string;
}

interface FormatAction {
  command: string;
  icon: React.ReactNode;
  label: string;
  shortcut?: string;
}

export default function RichTextEditor({
  content,
  onChange,
  onTextSelection,
  aiAssistanceEnabled = false,
  comments = [],
  className
}: RichTextEditorProps) {
  const [isEditing, setIsEditing] = useState(true);
  const [selectedText, setSelectedText] = useState('');
  const [showAiSuggestions, setShowAiSuggestions] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [characterCount, setCharacterCount] = useState(0);
  const editorRef = useRef<HTMLDivElement>(null);

  const formatActions: FormatAction[] = [
    { command: 'bold', icon: <Bold className="h-4 w-4" />, label: 'Bold', shortcut: 'Ctrl+B' },
    { command: 'italic', icon: <Italic className="h-4 w-4" />, label: 'Italic', shortcut: 'Ctrl+I' },
    { command: 'underline', icon: <Underline className="h-4 w-4" />, label: 'Underline', shortcut: 'Ctrl+U' },
    { command: 'insertUnorderedList', icon: <List className="h-4 w-4" />, label: 'Bullet List' },
    { command: 'insertOrderedList', icon: <ListOrdered className="h-4 w-4" />, label: 'Numbered List' },
    { command: 'formatBlock', icon: <Quote className="h-4 w-4" />, label: 'Quote' },
    { command: 'createLink', icon: <Link className="h-4 w-4" />, label: 'Link', shortcut: 'Ctrl+K' },
    { command: 'insertImage', icon: <Image className="h-4 w-4" />, label: 'Image' },
    { command: 'undo', icon: <Undo className="h-4 w-4" />, label: 'Undo', shortcut: 'Ctrl+Z' },
    { command: 'redo', icon: <Redo className="h-4 w-4" />, label: 'Redo', shortcut: 'Ctrl+Y' }
  ];

  useEffect(() => {
    updateStats(content);
  }, [content]);

  const updateStats = (text: string) => {
    const plainText = text.replace(/<[^>]*>/g, '');
    const words = plainText.split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    setCharacterCount(plainText.length);
  };

  const handleFormat = (command: string, value?: string) => {
    if (command === 'createLink') {
      const url = prompt('Enter URL:');
      if (url) {
        document.execCommand(command, false, url);
      }
    } else if (command === 'insertImage') {
      const url = prompt('Enter image URL:');
      if (url) {
        document.execCommand(command, false, url);
      }
    } else if (command === 'formatBlock') {
      document.execCommand(command, false, 'blockquote');
    } else {
      document.execCommand(command, false, value);
    }
    
    // Update content after formatting
    if (editorRef.current) {
      onChange(editorRef.current.innerHTML);
    }
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().length > 0) {
      const selectedText = selection.toString();
      const range = selection.getRangeAt(0);
      
      setSelectedText(selectedText);
      
      if (onTextSelection) {
        onTextSelection({
          start: range.startOffset,
          end: range.endOffset,
          text: selectedText
        });
      }
    }
  };

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      onChange(newContent);
      updateStats(newContent);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Handle keyboard shortcuts
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'b':
          e.preventDefault();
          handleFormat('bold');
          break;
        case 'i':
          e.preventDefault();
          handleFormat('italic');
          break;
        case 'u':
          e.preventDefault();
          handleFormat('underline');
          break;
        case 'k':
          e.preventDefault();
          handleFormat('createLink');
          break;
        case 'z':
          e.preventDefault();
          handleFormat('undo');
          break;
        case 'y':
          e.preventDefault();
          handleFormat('redo');
          break;
      }
    }
  };

  const getCommentIndicators = () => {
    return comments.filter(comment => comment.position && !comment.resolved);
  };

  const handleAiSuggestion = async (type: 'grammar' | 'style' | 'clarity' | 'tone') => {
    if (!aiAssistanceEnabled) return;
    
    setShowAiSuggestions(true);
    
    // Simulate AI suggestion (in production, this would call an AI service)
    setTimeout(() => {
      const suggestions = {
        grammar: 'Consider changing "it\'s" to "its" in the third paragraph.',
        style: 'The sentence could be more concise. Try: "AI transforms industries rapidly."',
        clarity: 'This paragraph might be clearer with shorter sentences.',
        tone: 'Consider using more active voice to strengthen the tone.'
      };
      
      alert(`AI Suggestion (${type}): ${suggestions[type]}`);
      setShowAiSuggestions(false);
    }, 1000);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center">
            <Type className="h-5 w-5 mr-2" />
            Content Editor
          </CardTitle>
          
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>Words: {wordCount}</span>
              <span>Characters: {characterCount}</span>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(!isEditing)}
            >
              {isEditing ? <Eye className="h-4 w-4 mr-2" /> : <Type className="h-4 w-4 mr-2" />}
              {isEditing ? 'Preview' : 'Edit'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Toolbar */}
        {isEditing && (
          <div className="border-b pb-4">
            <div className="flex flex-wrap items-center gap-1">
              {formatActions.map((action) => (
                <Button
                  key={action.command}
                  variant="outline"
                  size="sm"
                  onClick={() => handleFormat(action.command)}
                  title={`${action.label}${action.shortcut ? ` (${action.shortcut})` : ''}`}
                  className="h-8 w-8 p-0"
                >
                  {action.icon}
                </Button>
              ))}
              
              <div className="w-px h-6 bg-gray-300 mx-2" />
              
              {/* AI Assistance Buttons */}
              {aiAssistanceEnabled && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAiSuggestion('grammar')}
                    disabled={showAiSuggestions}
                    className="text-xs"
                  >
                    <Bot className="h-3 w-3 mr-1" />
                    Grammar
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAiSuggestion('style')}
                    disabled={showAiSuggestions}
                    className="text-xs"
                  >
                    <Bot className="h-3 w-3 mr-1" />
                    Style
                  </Button>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAiSuggestion('clarity')}
                    disabled={showAiSuggestions}
                    className="text-xs"
                  >
                    <Bot className="h-3 w-3 mr-1" />
                    Clarity
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        {/* Editor/Preview Area */}
        <div className="relative">
          {isEditing ? (
            <div
              ref={editorRef}
              contentEditable
              className="min-h-[400px] p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent prose max-w-none"
              dangerouslySetInnerHTML={{ __html: content }}
              onInput={handleContentChange}
              onMouseUp={handleTextSelection}
              onKeyUp={handleTextSelection}
              onKeyDown={handleKeyDown}
              style={{ outline: 'none' }}
            />
          ) : (
            <div className="min-h-[400px] p-4 border border-gray-300 rounded-lg prose max-w-none">
              <div dangerouslySetInnerHTML={{ __html: content }} />
            </div>
          )}
          
          {/* Comment Indicators */}
          {getCommentIndicators().map((comment) => (
            <div
              key={comment.id}
              className="absolute right-2 top-2 w-3 h-3 bg-yellow-400 rounded-full cursor-pointer"
              title={`Comment: ${comment.content.substring(0, 50)}...`}
            />
          ))}
        </div>

        {/* Selected Text Actions */}
        {selectedText && isEditing && (
          <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Selected:</span>
              <Badge variant="outline" className="max-w-xs truncate">
                "{selectedText}"
              </Badge>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (onTextSelection) {
                    onTextSelection({
                      start: 0,
                      end: selectedText.length,
                      text: selectedText
                    });
                  }
                }}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Add Comment
              </Button>
              
              {aiAssistanceEnabled && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleAiSuggestion('style')}
                  disabled={showAiSuggestions}
                >
                  <Bot className="h-4 w-4 mr-2" />
                  AI Improve
                </Button>
              )}
            </div>
          </div>
        )}

        {/* AI Suggestions Panel */}
        {showAiSuggestions && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Bot className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">AI is analyzing...</span>
            </div>
            <div className="w-full bg-blue-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
          </div>
        )}

        {/* Footer Stats */}
        <div className="flex justify-between items-center text-sm text-gray-600 pt-4 border-t">
          <div className="flex items-center space-x-4">
            <span>Last saved: {new Date().toLocaleTimeString()}</span>
            {comments.length > 0 && (
              <span className="flex items-center">
                <MessageSquare className="h-4 w-4 mr-1" />
                {comments.filter(c => !c.resolved).length} active comments
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {aiAssistanceEnabled && (
              <Badge variant="outline" className="text-blue-600">
                <Bot className="h-3 w-3 mr-1" />
                AI Enabled
              </Badge>
            )}
            <span>Auto-save enabled</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
