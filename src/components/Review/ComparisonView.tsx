/**
 * Comparison View Component
 * 
 * Side-by-side comparison of original and current content with change tracking
 */

'use client';

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { But<PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  GitCompare, 
  Plus, 
  Minus, 
  Edit,
  Eye,
  RotateCcw,
  Check,
  X,
  Clock
} from 'lucide-react';

interface ContentChange {
  id: string;
  type: 'addition' | 'deletion' | 'modification';
  position: {
    start: number;
    end: number;
  };
  originalText: string;
  newText: string;
  reason: string;
  timestamp: string;
}

interface ComparisonViewProps {
  originalContent: string;
  currentContent: string;
  changes: ContentChange[];
  onAcceptChange?: (changeId: string) => void;
  onRejectChange?: (changeId: string) => void;
  className?: string;
}

export default function ComparisonView({
  originalContent,
  currentContent,
  changes,
  onAcceptChange,
  onRejectChange,
  className
}: ComparisonViewProps) {
  const [viewMode, setViewMode] = useState<'side-by-side' | 'unified'>('side-by-side');
  const [showChangesOnly, setShowChangesOnly] = useState(false);
  const [selectedChange, setSelectedChange] = useState<string | null>(null);

  // Calculate diff statistics
  const diffStats = useMemo(() => {
    const additions = changes.filter(c => c.type === 'addition').length;
    const deletions = changes.filter(c => c.type === 'deletion').length;
    const modifications = changes.filter(c => c.type === 'modification').length;
    
    return { additions, deletions, modifications, total: changes.length };
  }, [changes]);

  // Generate highlighted content with changes
  const generateHighlightedContent = (content: string, isOriginal: boolean) => {
    let highlightedContent = content;
    
    // Sort changes by position (descending) to avoid offset issues
    const sortedChanges = [...changes].sort((a, b) => b.position.start - a.position.start);
    
    sortedChanges.forEach((change) => {
      const { start, end } = change.position;
      const beforeText = highlightedContent.substring(0, start);
      const afterText = highlightedContent.substring(end);
      
      let highlightedText = '';
      
      if (isOriginal) {
        if (change.type === 'deletion' || change.type === 'modification') {
          highlightedText = `<span class="bg-red-100 text-red-800 line-through" data-change-id="${change.id}">${change.originalText}</span>`;
        } else {
          highlightedText = change.originalText;
        }
      } else {
        if (change.type === 'addition' || change.type === 'modification') {
          highlightedText = `<span class="bg-green-100 text-green-800" data-change-id="${change.id}">${change.newText}</span>`;
        } else {
          highlightedText = '';
        }
      }
      
      highlightedContent = beforeText + highlightedText + afterText;
    });
    
    return highlightedContent;
  };

  const handleChangeClick = (changeId: string) => {
    setSelectedChange(selectedChange === changeId ? null : changeId);
  };

  const getChangeIcon = (type: ContentChange['type']) => {
    switch (type) {
      case 'addition':
        return <Plus className="h-4 w-4 text-green-600" />;
      case 'deletion':
        return <Minus className="h-4 w-4 text-red-600" />;
      case 'modification':
        return <Edit className="h-4 w-4 text-blue-600" />;
    }
  };

  const getChangeColor = (type: ContentChange['type']) => {
    switch (type) {
      case 'addition':
        return 'border-green-200 bg-green-50';
      case 'deletion':
        return 'border-red-200 bg-red-50';
      case 'modification':
        return 'border-blue-200 bg-blue-50';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle className="flex items-center">
                <GitCompare className="h-5 w-5 mr-2" />
                Content Comparison
              </CardTitle>
              <CardDescription>
                Compare original and current versions with tracked changes
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'side-by-side' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('side-by-side')}
              >
                Side by Side
              </Button>
              
              <Button
                variant={viewMode === 'unified' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('unified')}
              >
                Unified
              </Button>
              
              <Button
                variant={showChangesOnly ? 'default' : 'outline'}
                size="sm"
                onClick={() => setShowChangesOnly(!showChangesOnly)}
              >
                <Eye className="h-4 w-4 mr-2" />
                Changes Only
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Diff Statistics */}
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Plus className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">{diffStats.additions} additions</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Minus className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium">{diffStats.deletions} deletions</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Edit className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">{diffStats.modifications} modifications</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge variant="outline">
                {diffStats.total} total changes
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Comparison */}
      {viewMode === 'side-by-side' ? (
        <div className="grid grid-cols-2 gap-6">
          {/* Original Content */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Original</CardTitle>
              <CardDescription>
                Content before review changes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div 
                className="prose max-w-none text-sm leading-relaxed"
                dangerouslySetInnerHTML={{ 
                  __html: generateHighlightedContent(originalContent, true) 
                }}
                onClick={(e) => {
                  const target = e.target as HTMLElement;
                  const changeId = target.getAttribute('data-change-id');
                  if (changeId) handleChangeClick(changeId);
                }}
              />
            </CardContent>
          </Card>

          {/* Current Content */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current</CardTitle>
              <CardDescription>
                Content with review changes applied
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div 
                className="prose max-w-none text-sm leading-relaxed"
                dangerouslySetInnerHTML={{ 
                  __html: generateHighlightedContent(currentContent, false) 
                }}
                onClick={(e) => {
                  const target = e.target as HTMLElement;
                  const changeId = target.getAttribute('data-change-id');
                  if (changeId) handleChangeClick(changeId);
                }}
              />
            </CardContent>
          </Card>
        </div>
      ) : (
        /* Unified View */
        <Card>
          <CardHeader>
            <CardTitle>Unified Diff</CardTitle>
            <CardDescription>
              Combined view showing all changes in context
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 font-mono text-sm">
              {changes.map((change, index) => (
                <div key={change.id} className="border-l-4 border-gray-300 pl-4">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="outline" className="text-xs">
                      Line {index + 1}
                    </Badge>
                    {getChangeIcon(change.type)}
                    <span className="text-xs text-gray-600">
                      {change.type.charAt(0).toUpperCase() + change.type.slice(1)}
                    </span>
                  </div>
                  
                  {change.type === 'deletion' && (
                    <div className="bg-red-50 text-red-800 p-2 rounded text-xs">
                      - {change.originalText}
                    </div>
                  )}
                  
                  {change.type === 'addition' && (
                    <div className="bg-green-50 text-green-800 p-2 rounded text-xs">
                      + {change.newText}
                    </div>
                  )}
                  
                  {change.type === 'modification' && (
                    <div className="space-y-1">
                      <div className="bg-red-50 text-red-800 p-2 rounded text-xs">
                        - {change.originalText}
                      </div>
                      <div className="bg-green-50 text-green-800 p-2 rounded text-xs">
                        + {change.newText}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Changes List */}
      <Card>
        <CardHeader>
          <CardTitle>Change Details</CardTitle>
          <CardDescription>
            Review and manage individual changes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {changes.map((change) => (
              <div 
                key={change.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  getChangeColor(change.type)
                } ${selectedChange === change.id ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => handleChangeClick(change.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getChangeIcon(change.type)}
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <Badge variant="outline" className="text-xs">
                          {change.type.charAt(0).toUpperCase() + change.type.slice(1)}
                        </Badge>
                        <span className="text-xs text-gray-600 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {new Date(change.timestamp).toLocaleString()}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-700 mb-2">{change.reason}</p>
                      
                      <div className="space-y-1">
                        {change.originalText && (
                          <div className="text-xs">
                            <span className="font-medium">Original:</span> "{change.originalText}"
                          </div>
                        )}
                        {change.newText && (
                          <div className="text-xs">
                            <span className="font-medium">New:</span> "{change.newText}"
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {onAcceptChange && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onAcceptChange(change.id);
                        }}
                        className="text-green-600 border-green-300 hover:bg-green-50"
                      >
                        <Check className="h-4 w-4" />
                      </Button>
                    )}
                    
                    {onRejectChange && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          onRejectChange(change.id);
                        }}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-gray-600"
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
            
            {changes.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <GitCompare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No changes detected</p>
                <p className="text-sm">The content appears to be identical</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
