/**
 * Comment System Component
 * 
 * Inline commenting system with AI assistance and threaded discussions
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  MessageSquare, 
  Plus, 
  Reply, 
  Check, 
  X,
  Bot,
  User,
  Clock,
  MoreVertical,
  Edit,
  Trash2
} from 'lucide-react';

interface Comment {
  id: string;
  content: string;
  author: string;
  authorType: 'human' | 'ai';
  timestamp: string;
  position?: {
    start: number;
    end: number;
  };
  resolved: boolean;
  replies: Comment[];
}

interface CommentSystemProps {
  comments: Comment[];
  onAddComment: (content: string, position?: {start: number; end: number}) => void;
  selectedText?: {start: number; end: number; text: string} | null;
  aiAssistanceEnabled?: boolean;
  className?: string;
}

export default function CommentSystem({
  comments,
  onAddComment,
  selectedText,
  aiAssistanceEnabled = false,
  className
}: CommentSystemProps) {
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [filter, setFilter] = useState<'all' | 'unresolved' | 'resolved'>('all');
  const [aiSuggesting, setAiSuggesting] = useState(false);

  const filteredComments = comments.filter(comment => {
    switch (filter) {
      case 'unresolved':
        return !comment.resolved;
      case 'resolved':
        return comment.resolved;
      default:
        return true;
    }
  });

  const handleAddComment = () => {
    if (!newComment.trim()) return;
    
    onAddComment(
      newComment,
      selectedText ? { start: selectedText.start, end: selectedText.end } : undefined
    );
    
    setNewComment('');
  };

  const handleReply = (commentId: string) => {
    if (!replyContent.trim()) return;
    
    // In a real implementation, this would add a reply to the specific comment
    console.log(`Adding reply to comment ${commentId}: ${replyContent}`);
    
    setReplyingTo(null);
    setReplyContent('');
  };

  const handleAiSuggestComment = async () => {
    if (!selectedText || !aiAssistanceEnabled) return;
    
    setAiSuggesting(true);
    
    // Simulate AI suggestion
    setTimeout(() => {
      const suggestions = [
        "This section could benefit from more specific examples.",
        "Consider adding a transition sentence here for better flow.",
        "The tone here seems inconsistent with the rest of the content.",
        "This claim would be stronger with supporting evidence.",
        "Consider breaking this into shorter sentences for clarity."
      ];
      
      const suggestion = suggestions[Math.floor(Math.random() * suggestions.length)];
      setNewComment(suggestion);
      setAiSuggesting(false);
    }, 1500);
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const CommentCard = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (
    <div className={`${isReply ? 'ml-6 mt-2' : ''} space-y-2`}>
      <Card className={`${comment.resolved ? 'opacity-60' : ''} ${isReply ? 'border-l-4 border-l-blue-200' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-start justify-between mb-2">
            <div className="flex items-center space-x-2">
              {comment.authorType === 'ai' ? (
                <Bot className="h-4 w-4 text-blue-500" />
              ) : (
                <User className="h-4 w-4 text-gray-500" />
              )}
              <span className="font-medium text-sm">{comment.author}</span>
              <Badge variant={comment.authorType === 'ai' ? 'default' : 'outline'} className="text-xs">
                {comment.authorType === 'ai' ? 'AI' : 'Human'}
              </Badge>
              <span className="text-xs text-gray-500 flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatTimestamp(comment.timestamp)}
              </span>
            </div>
            
            <div className="flex items-center space-x-1">
              {comment.resolved && (
                <Badge variant="default" className="text-xs">
                  <Check className="h-3 w-3 mr-1" />
                  Resolved
                </Badge>
              )}
              
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </div>
          </div>
          
          <p className="text-sm text-gray-700 mb-3">{comment.content}</p>
          
          {comment.position && (
            <div className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mb-3">
              Referenced text: "{selectedText?.text || 'Selected text'}"
            </div>
          )}
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyingTo(comment.id)}
              className="text-xs"
            >
              <Reply className="h-3 w-3 mr-1" />
              Reply
            </Button>
            
            {!comment.resolved && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-green-600"
              >
                <Check className="h-3 w-3 mr-1" />
                Resolve
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              className="text-xs text-gray-500"
            >
              <Edit className="h-3 w-3 mr-1" />
              Edit
            </Button>
          </div>
          
          {/* Reply Form */}
          {replyingTo === comment.id && (
            <div className="mt-3 pt-3 border-t">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder="Write a reply..."
                className="w-full p-2 text-sm border border-gray-300 rounded-md resize-none"
                rows={2}
              />
              <div className="flex items-center space-x-2 mt-2">
                <Button
                  size="sm"
                  onClick={() => handleReply(comment.id)}
                  disabled={!replyContent.trim()}
                >
                  Reply
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setReplyingTo(null);
                    setReplyContent('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Replies */}
      {comment.replies.map((reply) => (
        <CommentCard key={reply.id} comment={reply} isReply={true} />
      ))}
    </div>
  );

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center">
              <MessageSquare className="h-5 w-5 mr-2" />
              Comments
            </CardTitle>
            <CardDescription>
              {comments.length} total, {comments.filter(c => !c.resolved).length} unresolved
            </CardDescription>
          </div>
          
          <div className="flex space-x-1 bg-gray-100 p-1 rounded">
            {[
              { id: 'all', label: 'All' },
              { id: 'unresolved', label: 'Open' },
              { id: 'resolved', label: 'Resolved' }
            ].map((filterOption) => (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id as any)}
                className={`px-2 py-1 text-xs rounded ${
                  filter === filterOption.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                {filterOption.label}
              </button>
            ))}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Add New Comment */}
        <div className="space-y-3">
          {selectedText && (
            <Alert>
              <MessageSquare className="h-4 w-4" />
              <AlertDescription>
                Adding comment to: "{selectedText.text.substring(0, 50)}..."
              </AlertDescription>
            </Alert>
          )}
          
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder={selectedText ? "Comment on selected text..." : "Add a general comment..."}
            className="w-full p-3 border border-gray-300 rounded-md resize-none"
            rows={3}
          />
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {aiAssistanceEnabled && selectedText && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAiSuggestComment}
                  disabled={aiSuggesting}
                >
                  {aiSuggesting ? (
                    <>
                      <Bot className="h-4 w-4 mr-2 animate-pulse" />
                      Suggesting...
                    </>
                  ) : (
                    <>
                      <Bot className="h-4 w-4 mr-2" />
                      AI Suggest
                    </>
                  )}
                </Button>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setNewComment('');
                }}
                disabled={!newComment.trim()}
              >
                Clear
              </Button>
              
              <Button
                size="sm"
                onClick={handleAddComment}
                disabled={!newComment.trim()}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Comment
              </Button>
            </div>
          </div>
        </div>
        
        {/* Comments List */}
        <div className="space-y-4 max-h-96 overflow-y-auto">
          {filteredComments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No comments yet</p>
              <p className="text-sm">Select text and add a comment to get started</p>
            </div>
          ) : (
            filteredComments.map((comment) => (
              <CommentCard key={comment.id} comment={comment} />
            ))
          )}
        </div>
        
        {/* Comment Statistics */}
        <div className="pt-4 border-t">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">{comments.length}</div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-600">
                {comments.filter(c => !c.resolved).length}
              </div>
              <div className="text-xs text-gray-600">Open</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">
                {comments.filter(c => c.resolved).length}
              </div>
              <div className="text-xs text-gray-600">Resolved</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
