/**
 * Review Metrics Component
 * 
 * Displays review performance metrics and AI cost tracking
 */

'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { 
  Clock, 
  DollarSign, 
  Target, 
  TrendingUp,
  Bot,
  User,
  CheckCircle,
  MessageSquare,
  Edit,
  BarChart3
} from 'lucide-react';

interface ReviewItem {
  id: string;
  title: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'needs_revision';
  submittedAt: string;
  reviewedAt?: string;
  comments: Array<{ id: string; resolved: boolean }>;
  changes: Array<{ id: string; type: string }>;
  qualityScore?: number;
  aiCosts?: {
    totalCost: number;
    assistanceCost: number;
    validationCost: number;
  };
}

interface ReviewMetricsProps {
  reviewItem: ReviewItem;
  aiAssistanceActive: boolean;
  className?: string;
}

export default function ReviewMetrics({ 
  reviewItem, 
  aiAssistanceActive, 
  className 
}: ReviewMetricsProps) {
  
  // Calculate review duration
  const getReviewDuration = () => {
    if (!reviewItem.reviewedAt) {
      const now = new Date();
      const submitted = new Date(reviewItem.submittedAt);
      return Math.floor((now.getTime() - submitted.getTime()) / (1000 * 60 * 60)); // hours
    }
    
    const reviewed = new Date(reviewItem.reviewedAt);
    const submitted = new Date(reviewItem.submittedAt);
    return Math.floor((reviewed.getTime() - submitted.getTime()) / (1000 * 60 * 60)); // hours
  };

  // Calculate completion percentage
  const getCompletionPercentage = () => {
    const totalComments = reviewItem.comments.length;
    const resolvedComments = reviewItem.comments.filter(c => c.resolved).length;
    
    if (totalComments === 0) return 100;
    return Math.round((resolvedComments / totalComments) * 100);
  };

  // Get quality score color
  const getQualityScoreColor = (score?: number) => {
    if (!score) return 'text-gray-500';
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'needs_revision': return 'text-orange-600';
      case 'in_review': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const reviewDuration = getReviewDuration();
  const completionPercentage = getCompletionPercentage();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Review Metrics
          </CardTitle>
          <CardDescription>
            Performance indicators and progress tracking
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {/* Review Duration */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Clock className="h-5 w-5 text-blue-600 mr-2" />
                <span className="font-medium">Duration</span>
              </div>
              <div className="text-2xl font-bold text-blue-600">
                {reviewDuration}h
              </div>
              <div className="text-xs text-gray-600">
                {reviewItem.reviewedAt ? 'Completed' : 'In progress'}
              </div>
            </div>

            {/* Quality Score */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Target className="h-5 w-5 text-purple-600 mr-2" />
                <span className="font-medium">Quality</span>
              </div>
              <div className={`text-2xl font-bold ${getQualityScoreColor(reviewItem.qualityScore)}`}>
                {reviewItem.qualityScore || '--'}/100
              </div>
              <div className="text-xs text-gray-600">
                Content score
              </div>
            </div>

            {/* Comments */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <MessageSquare className="h-5 w-5 text-orange-600 mr-2" />
                <span className="font-medium">Comments</span>
              </div>
              <div className="text-2xl font-bold text-orange-600">
                {reviewItem.comments.length}
              </div>
              <div className="text-xs text-gray-600">
                {reviewItem.comments.filter(c => c.resolved).length} resolved
              </div>
            </div>

            {/* Changes */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                <Edit className="h-5 w-5 text-green-600 mr-2" />
                <span className="font-medium">Changes</span>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {reviewItem.changes.length}
              </div>
              <div className="text-xs text-gray-600">
                Modifications made
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress Tracking */}
      <Card>
        <CardHeader>
          <CardTitle>Review Progress</CardTitle>
          <CardDescription>
            Current status and completion tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Status */}
          <div className="flex items-center justify-between">
            <span className="font-medium">Status</span>
            <Badge 
              variant={reviewItem.status === 'approved' ? 'default' : 'secondary'}
              className={getStatusColor(reviewItem.status)}
            >
              {reviewItem.status.replace('_', ' ').toUpperCase()}
            </Badge>
          </div>

          {/* Completion Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="font-medium">Completion</span>
              <span className="text-sm text-gray-600">{completionPercentage}%</span>
            </div>
            <Progress value={completionPercentage} className="h-2" />
            <div className="text-xs text-gray-600">
              {reviewItem.comments.filter(c => c.resolved).length} of {reviewItem.comments.length} comments resolved
            </div>
          </div>

          {/* Review Timeline */}
          <div className="space-y-3">
            <h4 className="font-medium">Timeline</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                <div className="flex-1">
                  <div className="text-sm font-medium">Submitted</div>
                  <div className="text-xs text-gray-600">
                    {new Date(reviewItem.submittedAt).toLocaleString()}
                  </div>
                </div>
              </div>
              
              {reviewItem.status !== 'pending' && (
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  <div className="flex-1">
                    <div className="text-sm font-medium">Review Started</div>
                    <div className="text-xs text-gray-600">
                      {new Date(reviewItem.submittedAt).toLocaleString()}
                    </div>
                  </div>
                </div>
              )}
              
              {reviewItem.reviewedAt && (
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div className="flex-1">
                    <div className="text-sm font-medium">Review Completed</div>
                    <div className="text-xs text-gray-600">
                      {new Date(reviewItem.reviewedAt).toLocaleString()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI Cost Tracking */}
      {reviewItem.aiCosts && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="h-5 w-5 mr-2" />
              AI Cost Tracking
            </CardTitle>
            <CardDescription>
              AI assistance and validation costs for this review
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-lg font-bold text-blue-600">
                  ${reviewItem.aiCosts.totalCost.toFixed(3)}
                </div>
                <div className="text-xs text-gray-600">Total Cost</div>
              </div>
              
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-lg font-bold text-green-600">
                  ${reviewItem.aiCosts.assistanceCost.toFixed(3)}
                </div>
                <div className="text-xs text-gray-600">Assistance</div>
              </div>
              
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <div className="text-lg font-bold text-purple-600">
                  ${reviewItem.aiCosts.validationCost.toFixed(3)}
                </div>
                <div className="text-xs text-gray-600">Validation</div>
              </div>
            </div>

            {/* Cost Breakdown */}
            <div className="space-y-2">
              <div className="flex justify-between items-center text-sm">
                <span className="flex items-center">
                  <Bot className="h-4 w-4 mr-2 text-blue-500" />
                  AI Assistance
                </span>
                <span className="font-medium">
                  ${reviewItem.aiCosts.assistanceCost.toFixed(3)}
                </span>
              </div>
              
              <div className="flex justify-between items-center text-sm">
                <span className="flex items-center">
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Quality Validation
                </span>
                <span className="font-medium">
                  ${reviewItem.aiCosts.validationCost.toFixed(3)}
                </span>
              </div>
            </div>

            {/* AI Assistance Status */}
            {aiAssistanceActive && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Bot className="h-4 w-4 text-blue-600" />
                  <span className="text-sm font-medium text-blue-800">
                    AI Assistance Active
                  </span>
                </div>
                <div className="text-xs text-blue-600 mt-1">
                  Real-time suggestions and analysis are being provided
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Performance Insights
          </CardTitle>
          <CardDescription>
            Review efficiency and quality indicators
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Review Efficiency */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">Review Efficiency</div>
                <div className="text-sm text-gray-600">
                  {reviewDuration < 24 ? 'Excellent' : reviewDuration < 48 ? 'Good' : 'Needs Improvement'}
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">
                  {reviewDuration < 24 ? '95%' : reviewDuration < 48 ? '80%' : '65%'}
                </div>
                <div className="text-xs text-gray-600">Efficiency Score</div>
              </div>
            </div>

            {/* Comment Resolution Rate */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">Comment Resolution</div>
                <div className="text-sm text-gray-600">
                  Progress on addressing feedback
                </div>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">{completionPercentage}%</div>
                <div className="text-xs text-gray-600">Resolved</div>
              </div>
            </div>

            {/* Quality Improvement */}
            {reviewItem.qualityScore && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium">Quality Impact</div>
                  <div className="text-sm text-gray-600">
                    Content quality enhancement
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-lg font-bold ${getQualityScoreColor(reviewItem.qualityScore)}`}>
                    {reviewItem.qualityScore >= 85 ? '+15%' : reviewItem.qualityScore >= 70 ? '+8%' : '+3%'}
                  </div>
                  <div className="text-xs text-gray-600">Improvement</div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
