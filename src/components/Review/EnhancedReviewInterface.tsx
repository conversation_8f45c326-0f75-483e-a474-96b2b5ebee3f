/**
 * Enhanced Review Interface
 * 
 * Advanced review interface with rich text editing, side-by-side comparison,
 * inline commenting, and AI cost tracking
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Eye, 
  Edit3, 
  MessageSquare, 
  CheckCircle, 
  XCircle,
  Clock,
  DollarSign,
  Split,
  Maximize2,
  Save,
  RefreshCw,
  User,
  Bot
} from 'lucide-react';
import RichTextEditor from './RichTextEditor';
import CommentSystem from './CommentSystem';
import ComparisonView from './ComparisonView';
import ReviewMetrics from './ReviewMetrics';

interface ReviewItem {
  id: string;
  title: string;
  content: string;
  originalContent: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'needs_revision';
  reviewerId?: string;
  reviewerName?: string;
  submittedAt: string;
  reviewedAt?: string;
  comments: ReviewComment[];
  changes: ContentChange[];
  qualityScore?: number;
  aiCosts?: {
    totalCost: number;
    assistanceCost: number;
    validationCost: number;
  };
}

interface ReviewComment {
  id: string;
  content: string;
  author: string;
  authorType: 'human' | 'ai';
  timestamp: string;
  position?: {
    start: number;
    end: number;
  };
  resolved: boolean;
  replies: ReviewComment[];
}

interface ContentChange {
  id: string;
  type: 'addition' | 'deletion' | 'modification';
  position: {
    start: number;
    end: number;
  };
  originalText: string;
  newText: string;
  reason: string;
  timestamp: string;
}

interface EnhancedReviewInterfaceProps {
  reviewItem: ReviewItem;
  onStatusChange: (status: ReviewItem['status']) => void;
  onContentChange: (content: string) => void;
  onCommentAdd: (comment: Omit<ReviewComment, 'id' | 'timestamp'>) => void;
  onSave: () => void;
  className?: string;
}

export default function EnhancedReviewInterface({
  reviewItem,
  onStatusChange,
  onContentChange,
  onCommentAdd,
  onSave,
  className
}: EnhancedReviewInterfaceProps) {
  const [viewMode, setViewMode] = useState<'edit' | 'preview' | 'comparison'>('edit');
  const [showComments, setShowComments] = useState(true);
  const [selectedText, setSelectedText] = useState<{start: number; end: number; text: string} | null>(null);
  const [aiAssistanceActive, setAiAssistanceActive] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleStatusChange = async (newStatus: ReviewItem['status']) => {
    try {
      await onStatusChange(newStatus);
      
      // Track AI cost for status change if AI assistance was used
      if (aiAssistanceActive) {
        // This would integrate with the cost tracking system
        console.log('AI assistance cost tracked for status change');
      }
    } catch (error) {
      console.error('Failed to change status:', error);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await onSave();
    } catch (error) {
      console.error('Failed to save:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleTextSelection = (selection: {start: number; end: number; text: string}) => {
    setSelectedText(selection);
  };

  const handleAddComment = (content: string, position?: {start: number; end: number}) => {
    const comment: Omit<ReviewComment, 'id' | 'timestamp'> = {
      content,
      author: 'Current Reviewer', // This would come from auth context
      authorType: 'human',
      position,
      resolved: false,
      replies: []
    };
    
    onCommentAdd(comment);
    setSelectedText(null);
  };

  const getStatusColor = (status: ReviewItem['status']) => {
    switch (status) {
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'needs_revision': return 'text-orange-600';
      case 'in_review': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: ReviewItem['status']) => {
    switch (status) {
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'needs_revision': return 'secondary';
      case 'in_review': return 'default';
      default: return 'outline';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="flex items-center">
                <Edit3 className="h-5 w-5 mr-2" />
                {reviewItem.title}
              </CardTitle>
              <CardDescription className="mt-2">
                <div className="flex items-center space-x-4">
                  <span>Submitted: {new Date(reviewItem.submittedAt).toLocaleDateString()}</span>
                  <Badge variant={getStatusBadge(reviewItem.status)}>
                    {reviewItem.status.replace('_', ' ').toUpperCase()}
                  </Badge>
                  {reviewItem.qualityScore && (
                    <Badge variant="outline">
                      Quality: {reviewItem.qualityScore}/100
                    </Badge>
                  )}
                </div>
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              {reviewItem.aiCosts && (
                <Badge variant="outline" className="flex items-center">
                  <DollarSign className="h-3 w-3 mr-1" />
                  ${reviewItem.aiCosts.totalCost.toFixed(3)}
                </Badge>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAiAssistanceActive(!aiAssistanceActive)}
                className={aiAssistanceActive ? 'bg-blue-50 border-blue-200' : ''}
              >
                <Bot className="h-4 w-4 mr-2" />
                AI Assist
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                disabled={saving}
              >
                {saving ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* View Mode Selector */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { id: 'edit', label: 'Edit', icon: <Edit3 className="h-4 w-4" /> },
                { id: 'preview', label: 'Preview', icon: <Eye className="h-4 w-4" /> },
                { id: 'comparison', label: 'Compare', icon: <Split className="h-4 w-4" /> }
              ].map((mode) => (
                <button
                  key={mode.id}
                  onClick={() => setViewMode(mode.id as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    viewMode === mode.id
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {mode.icon}
                  <span>{mode.label}</span>
                </button>
              ))}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowComments(!showComments)}
                className={showComments ? 'bg-blue-50 border-blue-200' : ''}
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                Comments ({reviewItem.comments.length})
              </Button>
              
              <Button
                variant="outline"
                size="sm"
              >
                <Maximize2 className="h-4 w-4 mr-2" />
                Full Screen
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Content Editor/Viewer */}
        <div className={`${showComments ? 'lg:col-span-3' : 'lg:col-span-4'}`}>
          {viewMode === 'edit' && (
            <RichTextEditor
              content={reviewItem.content}
              onChange={onContentChange}
              onTextSelection={handleTextSelection}
              aiAssistanceEnabled={aiAssistanceActive}
              comments={reviewItem.comments}
            />
          )}
          
          {viewMode === 'preview' && (
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
                <CardDescription>
                  How the content will appear to readers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div 
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: reviewItem.content }}
                />
              </CardContent>
            </Card>
          )}
          
          {viewMode === 'comparison' && (
            <ComparisonView
              originalContent={reviewItem.originalContent}
              currentContent={reviewItem.content}
              changes={reviewItem.changes}
            />
          )}
        </div>

        {/* Comments Sidebar */}
        {showComments && (
          <div className="lg:col-span-1">
            <CommentSystem
              comments={reviewItem.comments}
              onAddComment={handleAddComment}
              selectedText={selectedText}
              aiAssistanceEnabled={aiAssistanceActive}
            />
          </div>
        )}
      </div>

      {/* Review Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Review Actions</CardTitle>
          <CardDescription>
            Make a decision on this content submission
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              <Button
                onClick={() => handleStatusChange('approved')}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Approve
              </Button>
              
              <Button
                onClick={() => handleStatusChange('needs_revision')}
                variant="outline"
                className="border-orange-300 text-orange-600 hover:bg-orange-50"
              >
                <Clock className="h-4 w-4 mr-2" />
                Request Revision
              </Button>
              
              <Button
                onClick={() => handleStatusChange('rejected')}
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50"
              >
                <XCircle className="h-4 w-4 mr-2" />
                Reject
              </Button>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-1" />
                <span>Reviewer: {reviewItem.reviewerName || 'Unassigned'}</span>
              </div>
              
              {reviewItem.reviewedAt && (
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>Reviewed: {new Date(reviewItem.reviewedAt).toLocaleDateString()}</span>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Review Metrics */}
      <ReviewMetrics
        reviewItem={reviewItem}
        aiAssistanceActive={aiAssistanceActive}
      />

      {/* AI Assistance Panel */}
      {aiAssistanceActive && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-800">
              <Bot className="h-5 w-5 mr-2" />
              AI Review Assistant
            </CardTitle>
            <CardDescription className="text-blue-600">
              AI-powered suggestions and analysis for content review
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Alert>
                <Bot className="h-4 w-4" />
                <AlertDescription>
                  AI assistance is active. Suggestions and automated analysis will be provided.
                  Current session cost: ${reviewItem.aiCosts?.assistanceCost.toFixed(3) || '0.000'}
                </AlertDescription>
              </Alert>
              
              <div className="grid grid-cols-2 gap-4">
                <Button variant="outline" size="sm" className="justify-start">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Grammar Check
                </Button>
                
                <Button variant="outline" size="sm" className="justify-start">
                  <Eye className="h-4 w-4 mr-2" />
                  Style Analysis
                </Button>
                
                <Button variant="outline" size="sm" className="justify-start">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Suggest Improvements
                </Button>
                
                <Button variant="outline" size="sm" className="justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Quality Score
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
