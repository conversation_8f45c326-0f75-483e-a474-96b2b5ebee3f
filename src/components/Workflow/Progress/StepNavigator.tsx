/**
 * Step Navigator Component
 * Interactive step navigation with breadcrumbs and quick access
 */

'use client';

import React, { useState } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface NavigationStep extends StepProgressInfo {
  canNavigate?: boolean;
  isClickable?: boolean;
  category?: string;
}

interface StepNavigatorProps {
  steps: NavigationStep[];
  currentStep?: string;
  onStepSelect?: (stepId: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  showCategories?: boolean;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export function StepNavigator({
  steps,
  currentStep,
  onStepSelect,
  onNext,
  onPrevious,
  showCategories = false,
  showProgress = true,
  compact = false,
  className = ''
}: StepNavigatorProps) {
  const [hoveredStep, setHoveredStep] = useState<string | null>(null);

  const currentIndex = steps.findIndex(step => step.stepId === currentStep);
  const canGoNext = currentIndex < steps.length - 1 && onNext;
  const canGoPrevious = currentIndex > 0 && onPrevious;

  const getStepStatus = (step: NavigationStep, index: number) => {
    if (step.stepId === currentStep) return 'current';
    if (step.status === 'completed') return 'completed';
    if (step.status === 'failed') return 'failed';
    if (step.status === 'running') return 'running';
    if (step.status === 'waiting_review' || step.status === 'waiting_approval') return 'waiting';
    if (index < currentIndex) return 'completed';
    return 'pending';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'bg-blue-500 text-white border-blue-600';
      case 'completed': return 'bg-green-500 text-white border-green-600';
      case 'failed': return 'bg-red-500 text-white border-red-600';
      case 'running': return 'bg-blue-400 text-white border-blue-500 animate-pulse';
      case 'waiting': return 'bg-yellow-500 text-white border-yellow-600';
      case 'pending': return 'bg-gray-200 text-gray-600 border-gray-300';
      default: return 'bg-gray-200 text-gray-600 border-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'current': return '▶';
      case 'completed': return '✓';
      case 'failed': return '✗';
      case 'running': return '⟳';
      case 'waiting': return '⏳';
      case 'pending': return '○';
      default: return '○';
    }
  };

  const handleStepClick = (step: NavigationStep, index: number) => {
    if (step.canNavigate !== false && step.isClickable !== false) {
      onStepSelect?.(step.stepId);
    }
  };

  const groupedSteps = showCategories 
    ? steps.reduce((groups, step, index) => {
        const category = step.category || 'General';
        if (!groups[category]) groups[category] = [];
        groups[category].push({ ...step, index });
        return groups;
      }, {} as Record<string, (NavigationStep & { index: number })[]>)
    : { 'All Steps': steps.map((step, index) => ({ ...step, index })) };

  if (compact) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {/* Previous Button */}
        <button
          onClick={onPrevious}
          disabled={!canGoPrevious}
          className={`p-2 rounded ${
            canGoPrevious 
              ? 'bg-gray-100 hover:bg-gray-200 text-gray-700' 
              : 'bg-gray-50 text-gray-400 cursor-not-allowed'
          }`}
          title="Previous step"
        >
          ←
        </button>

        {/* Current Step Indicator */}
        <div className="flex items-center space-x-2 px-3 py-1 bg-blue-50 rounded-lg">
          <span className="text-sm font-medium text-blue-900">
            {currentIndex + 1} / {steps.length}
          </span>
          {currentStep && (
            <span className="text-sm text-blue-700">
              {steps.find(s => s.stepId === currentStep)?.stepName || currentStep}
            </span>
          )}
        </div>

        {/* Next Button */}
        <button
          onClick={onNext}
          disabled={!canGoNext}
          className={`p-2 rounded ${
            canGoNext 
              ? 'bg-gray-100 hover:bg-gray-200 text-gray-700' 
              : 'bg-gray-50 text-gray-400 cursor-not-allowed'
          }`}
          title="Next step"
        >
          →
        </button>

        {/* Progress */}
        {showProgress && (
          <div className="flex-1 max-w-32">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentIndex + 1) / steps.length) * 100}%` }}
              />
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Step Navigator</h3>
          <p className="text-sm text-gray-600">
            Step {currentIndex + 1} of {steps.length}
          </p>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center space-x-2">
          <button
            onClick={onPrevious}
            disabled={!canGoPrevious}
            className={`px-3 py-1 text-sm rounded ${
              canGoPrevious 
                ? 'bg-gray-100 hover:bg-gray-200 text-gray-700' 
                : 'bg-gray-50 text-gray-400 cursor-not-allowed'
            }`}
          >
            ← Previous
          </button>
          <button
            onClick={onNext}
            disabled={!canGoNext}
            className={`px-3 py-1 text-sm rounded ${
              canGoNext 
                ? 'bg-blue-100 hover:bg-blue-200 text-blue-700' 
                : 'bg-gray-50 text-gray-400 cursor-not-allowed'
            }`}
          >
            Next →
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {showProgress && (
        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${((currentIndex + 1) / steps.length) * 100}%` }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Start</span>
            <span>{Math.round(((currentIndex + 1) / steps.length) * 100)}%</span>
            <span>Complete</span>
          </div>
        </div>
      )}

      {/* Step Groups */}
      <div className="space-y-6">
        {Object.entries(groupedSteps).map(([category, categorySteps]) => (
          <div key={category}>
            {showCategories && Object.keys(groupedSteps).length > 1 && (
              <h4 className="text-sm font-medium text-gray-900 mb-3">{category}</h4>
            )}
            
            {/* Steps Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              {categorySteps.map((step) => {
                const status = getStepStatus(step, step.index);
                const isClickable = step.canNavigate !== false && step.isClickable !== false;
                
                return (
                  <div
                    key={step.stepId}
                    className={`relative p-3 rounded-lg border transition-all duration-200 ${
                      isClickable ? 'cursor-pointer hover:shadow-md' : 'cursor-default'
                    } ${
                      step.stepId === hoveredStep ? 'ring-2 ring-blue-300' : ''
                    }`}
                    onClick={() => handleStepClick(step, step.index)}
                    onMouseEnter={() => setHoveredStep(step.stepId)}
                    onMouseLeave={() => setHoveredStep(null)}
                  >
                    {/* Step Number and Status */}
                    <div className="flex items-center space-x-3 mb-2">
                      <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-bold ${getStatusColor(status)}`}>
                        {step.index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {step.stepName || step.stepId}
                        </div>
                        <div className="text-xs text-gray-500 capitalize">
                          {step.status.replace('_', ' ')}
                        </div>
                      </div>
                      <div className="text-lg">
                        {getStatusIcon(status)}
                      </div>
                    </div>

                    {/* Duration */}
                    {step.duration && (
                      <div className="text-xs text-gray-500">
                        Duration: {Math.round(step.duration / 1000)}s
                      </div>
                    )}

                    {/* Error Indicator */}
                    {step.error && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700">
                        {step.error}
                      </div>
                    )}

                    {/* Current Step Indicator */}
                    {step.stepId === currentStep && (
                      <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full animate-pulse" />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            {steps.filter(s => s.status === 'completed').length} completed, {' '}
            {steps.filter(s => s.status === 'failed').length} failed, {' '}
            {steps.filter(s => s.status === 'pending').length} pending
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                const firstPending = steps.find(s => s.status === 'pending');
                if (firstPending) onStepSelect?.(firstPending.stepId);
              }}
              className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
            >
              Next Pending
            </button>
            <button
              onClick={() => {
                const firstFailed = steps.find(s => s.status === 'failed');
                if (firstFailed) onStepSelect?.(firstFailed.stepId);
              }}
              className="px-3 py-1 text-xs bg-red-100 hover:bg-red-200 text-red-700 rounded"
            >
              Show Failed
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StepNavigator;
