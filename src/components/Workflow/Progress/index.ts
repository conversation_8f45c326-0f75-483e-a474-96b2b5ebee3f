/**
 * Workflow Progress Components
 * Export all progress visualization components
 */

export { default as WorkflowTimeline } from './WorkflowTimeline';
export { default as StepNavigator } from './StepNavigator';
export { default as ProgressIndicator } from './ProgressIndicator';
export { default as EstimatedTimeDisplay } from './EstimatedTimeDisplay';
export { default as CostTracker } from './CostTracker';
export { default as WorkflowFlowChart } from './WorkflowFlowChart';

// Component prop interfaces
export interface WorkflowTimelineProps {
  steps: any[];
  events: any[];
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  showEstimates?: boolean;
  showDependencies?: boolean;
  className?: string;
}

export interface StepNavigatorProps {
  steps: any[];
  currentStep?: string;
  onStepSelect?: (stepId: string) => void;
  onNext?: () => void;
  onPrevious?: () => void;
  showCategories?: boolean;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export interface ProgressIndicatorProps {
  steps: any[];
  currentStep?: string;
  showMetrics?: boolean;
  showEstimates?: boolean;
  showStepBreakdown?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'linear' | 'circular' | 'stepped';
  className?: string;
}

export interface EstimatedTimeDisplayProps {
  steps: any[];
  startTime?: string;
  currentStep?: string;
  showConfidence?: boolean;
  showBreakdown?: boolean;
  updateInterval?: number;
  className?: string;
}

export interface CostTrackerProps {
  executionId?: string;
  steps: any[];
  budget?: number;
  alertThreshold?: number;
  showBreakdown?: boolean;
  showTokens?: boolean;
  showAlerts?: boolean;
  updateInterval?: number;
  className?: string;
}

export interface WorkflowFlowChartProps {
  steps: any[];
  dependencies?: Record<string, string[]>;
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  showLabels?: boolean;
  showConnections?: boolean;
  layout?: 'horizontal' | 'vertical';
  className?: string;
}
