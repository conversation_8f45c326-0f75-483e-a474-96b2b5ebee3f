/**
 * Estimated Time Display Component
 * Shows time estimates, elapsed time, and completion predictions
 */

'use client';

import React, { useState, useEffect } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface TimeEstimate {
  totalEstimated: number;
  elapsed: number;
  remaining: number;
  completionTime: Date;
  confidence: number;
}

interface EstimatedTimeDisplayProps {
  steps: StepProgressInfo[];
  startTime?: string;
  currentStep?: string;
  showConfidence?: boolean;
  showBreakdown?: boolean;
  updateInterval?: number;
  className?: string;
}

export function EstimatedTimeDisplay({
  steps,
  startTime,
  currentStep,
  showConfidence = true,
  showBreakdown = false,
  updateInterval = 1000,
  className = ''
}: EstimatedTimeDisplayProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [estimates, setEstimates] = useState<TimeEstimate | null>(null);

  // Update current time periodically
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, updateInterval);

    return () => clearInterval(timer);
  }, [updateInterval]);

  // Calculate time estimates
  useEffect(() => {
    if (!startTime || steps.length === 0) {
      setEstimates(null);
      return;
    }

    const start = new Date(startTime);
    const elapsed = currentTime.getTime() - start.getTime();
    
    const completedSteps = steps.filter(s => s.status === 'completed');
    const runningSteps = steps.filter(s => s.status === 'running');
    const pendingSteps = steps.filter(s => s.status === 'pending');
    
    // Calculate average step duration from completed steps
    let avgStepDuration = 0;
    let confidence = 0;
    
    if (completedSteps.length > 0) {
      const totalCompletedDuration = completedSteps.reduce((sum, step) => {
        return sum + (step.duration || 0);
      }, 0);
      avgStepDuration = totalCompletedDuration / completedSteps.length;
      confidence = Math.min(completedSteps.length / steps.length, 0.9);
    } else {
      // Fallback to default estimates
      avgStepDuration = 120000; // 2 minutes default
      confidence = 0.1;
    }

    // Estimate remaining time
    const remainingSteps = pendingSteps.length + runningSteps.length;
    const estimatedRemaining = remainingSteps * avgStepDuration;
    
    // Add buffer for running steps (assume they're halfway done)
    const runningStepBuffer = runningSteps.length * (avgStepDuration * 0.5);
    const totalRemaining = estimatedRemaining - runningStepBuffer;
    
    const totalEstimated = elapsed + Math.max(totalRemaining, 0);
    const completionTime = new Date(start.getTime() + totalEstimated);

    setEstimates({
      totalEstimated,
      elapsed,
      remaining: Math.max(totalRemaining, 0),
      completionTime,
      confidence
    });
  }, [steps, startTime, currentTime]);

  const formatDuration = (ms: number, includeSeconds = true) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return includeSeconds 
        ? `${hours}h ${minutes % 60}m ${seconds % 60}s`
        : `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return includeSeconds 
        ? `${minutes}m ${seconds % 60}s`
        : `${minutes}m`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.7) return 'text-green-600';
    if (confidence >= 0.4) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getConfidenceLabel = (confidence: number) => {
    if (confidence >= 0.7) return 'High';
    if (confidence >= 0.4) return 'Medium';
    return 'Low';
  };

  const getStepEstimates = () => {
    if (!estimates) return [];
    
    const completedSteps = steps.filter(s => s.status === 'completed');
    const avgDuration = completedSteps.length > 0 
      ? completedSteps.reduce((sum, step) => sum + (step.duration || 0), 0) / completedSteps.length
      : 120000;

    return steps.map(step => ({
      ...step,
      estimatedDuration: step.duration || avgDuration,
      isEstimate: !step.duration
    }));
  };

  if (!estimates) {
    return (
      <div className={`bg-gray-50 rounded-lg p-4 ${className}`}>
        <div className="text-center text-gray-500">
          <div className="text-sm">Time estimates unavailable</div>
          <div className="text-xs mt-1">Workflow not started or no timing data</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Time Estimates</h3>
        {showConfidence && (
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">Confidence:</span>
            <span className={`text-sm font-medium ${getConfidenceColor(estimates.confidence)}`}>
              {getConfidenceLabel(estimates.confidence)} ({Math.round(estimates.confidence * 100)}%)
            </span>
          </div>
        )}
      </div>

      {/* Main Time Display */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Elapsed Time */}
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {formatDuration(estimates.elapsed, false)}
          </div>
          <div className="text-sm text-gray-600">Elapsed</div>
          <div className="text-xs text-gray-500 mt-1">
            Since {startTime ? formatTime(new Date(startTime)) : 'start'}
          </div>
        </div>

        {/* Remaining Time */}
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {formatDuration(estimates.remaining, false)}
          </div>
          <div className="text-sm text-gray-600">Remaining</div>
          <div className="text-xs text-gray-500 mt-1">
            {estimates.remaining > 0 ? 'Estimated' : 'Complete'}
          </div>
        </div>

        {/* Completion Time */}
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {formatTime(estimates.completionTime)}
          </div>
          <div className="text-sm text-gray-600">Completion</div>
          <div className="text-xs text-gray-500 mt-1">
            {estimates.completionTime.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Progress Bar with Time Markers */}
      <div className="mb-6">
        <div className="relative">
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-500 h-3 rounded-full transition-all duration-500"
              style={{ 
                width: `${Math.min((estimates.elapsed / estimates.totalEstimated) * 100, 100)}%` 
              }}
            />
          </div>
          
          {/* Time markers */}
          <div className="flex justify-between text-xs text-gray-500 mt-2">
            <span>Start</span>
            <span className="text-blue-600 font-medium">
              {Math.round((estimates.elapsed / estimates.totalEstimated) * 100)}%
            </span>
            <span>End</span>
          </div>
        </div>
      </div>

      {/* Step Breakdown */}
      {showBreakdown && (
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-3">Step Breakdown</h4>
          <div className="space-y-2">
            {getStepEstimates().map((step, index) => (
              <div key={step.stepId} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center text-xs font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {step.stepName || step.stepId}
                    </div>
                    <div className="text-xs text-gray-500 capitalize">
                      {step.status.replace('_', ' ')}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatDuration(step.estimatedDuration, false)}
                  </div>
                  {step.isEstimate && (
                    <div className="text-xs text-gray-500">Estimated</div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Live Clock */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Current time:</span>
          <span className="font-mono">{formatTime(currentTime)}</span>
        </div>
      </div>
    </div>
  );
}

export default EstimatedTimeDisplay;
