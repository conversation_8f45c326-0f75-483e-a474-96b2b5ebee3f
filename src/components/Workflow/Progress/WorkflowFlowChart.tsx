/**
 * Workflow Flow Chart Component
 * Visual flow chart representation of workflow steps and dependencies
 */

'use client';

import React, { useState, useMemo } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface FlowNode extends StepProgressInfo {
  x: number;
  y: number;
  width: number;
  height: number;
  dependencies?: string[];
  level: number;
}

interface FlowConnection {
  from: string;
  to: string;
  fromX: number;
  fromY: number;
  toX: number;
  toY: number;
}

interface WorkflowFlowChartProps {
  steps: StepProgressInfo[];
  dependencies?: Record<string, string[]>;
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  showLabels?: boolean;
  showConnections?: boolean;
  layout?: 'horizontal' | 'vertical';
  className?: string;
}

export function WorkflowFlowChart({
  steps,
  dependencies = {},
  currentStep,
  onStepClick,
  showLabels = true,
  showConnections = true,
  layout = 'horizontal',
  className = ''
}: WorkflowFlowChartProps) {
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [hoveredStep, setHoveredStep] = useState<string | null>(null);

  const nodeWidth = 120;
  const nodeHeight = 60;
  const levelSpacing = layout === 'horizontal' ? 180 : 100;
  const nodeSpacing = layout === 'horizontal' ? 80 : 140;

  // Calculate node positions and levels
  const flowNodes = useMemo(() => {
    const nodes: FlowNode[] = [];
    const levels: Record<number, string[]> = {};
    const stepLevels: Record<string, number> = {};

    // Calculate dependency levels
    const calculateLevel = (stepId: string, visited = new Set<string>()): number => {
      if (visited.has(stepId)) return 0; // Circular dependency protection
      if (stepLevels[stepId] !== undefined) return stepLevels[stepId];

      visited.add(stepId);
      const stepDeps = dependencies[stepId] || [];
      
      if (stepDeps.length === 0) {
        stepLevels[stepId] = 0;
      } else {
        const maxDepLevel = Math.max(...stepDeps.map(dep => calculateLevel(dep, visited)));
        stepLevels[stepId] = maxDepLevel + 1;
      }

      visited.delete(stepId);
      return stepLevels[stepId];
    };

    // Calculate levels for all steps
    steps.forEach(step => {
      const level = calculateLevel(step.stepId);
      if (!levels[level]) levels[level] = [];
      levels[level].push(step.stepId);
    });

    // Position nodes
    Object.entries(levels).forEach(([levelStr, stepIds]) => {
      const level = parseInt(levelStr);
      stepIds.forEach((stepId, index) => {
        const step = steps.find(s => s.stepId === stepId);
        if (!step) return;

        const x = layout === 'horizontal' 
          ? level * levelSpacing + 50
          : (index - (stepIds.length - 1) / 2) * nodeSpacing + 300;
        
        const y = layout === 'horizontal'
          ? (index - (stepIds.length - 1) / 2) * nodeSpacing + 200
          : level * levelSpacing + 50;

        nodes.push({
          ...step,
          x,
          y,
          width: nodeWidth,
          height: nodeHeight,
          dependencies: dependencies[stepId] || [],
          level
        });
      });
    });

    return nodes;
  }, [steps, dependencies, layout]);

  // Calculate connections
  const connections = useMemo(() => {
    const conns: FlowConnection[] = [];
    
    flowNodes.forEach(node => {
      node.dependencies.forEach(depId => {
        const depNode = flowNodes.find(n => n.stepId === depId);
        if (depNode) {
          conns.push({
            from: depId,
            to: node.stepId,
            fromX: depNode.x + depNode.width,
            fromY: depNode.y + depNode.height / 2,
            toX: node.x,
            toY: node.y + node.height / 2
          });
        }
      });
    });

    return conns;
  }, [flowNodes]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500 border-green-600 text-white';
      case 'running': return 'bg-blue-500 border-blue-600 text-white';
      case 'failed': return 'bg-red-500 border-red-600 text-white';
      case 'waiting_review': return 'bg-purple-500 border-purple-600 text-white';
      case 'waiting_approval': return 'bg-orange-500 border-orange-600 text-white';
      case 'pending': return 'bg-gray-200 border-gray-300 text-gray-700';
      default: return 'bg-gray-200 border-gray-300 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✓';
      case 'running': return '⟳';
      case 'failed': return '✗';
      case 'waiting_review': return '👥';
      case 'waiting_approval': return '⏳';
      case 'pending': return '○';
      default: return '○';
    }
  };

  const handleStepClick = (stepId: string) => {
    setSelectedStep(selectedStep === stepId ? null : stepId);
    onStepClick?.(stepId);
  };

  // Calculate SVG dimensions
  const maxX = Math.max(...flowNodes.map(n => n.x + n.width)) + 50;
  const maxY = Math.max(...flowNodes.map(n => n.y + n.height)) + 50;
  const minX = Math.min(...flowNodes.map(n => n.x)) - 50;
  const minY = Math.min(...flowNodes.map(n => n.y)) - 50;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Workflow Flow Chart</h3>
          <p className="text-sm text-gray-600">
            Visual representation of workflow steps and dependencies
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setSelectedStep(null)}
            className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
          >
            Clear Selection
          </button>
        </div>
      </div>

      {/* Flow Chart */}
      <div className="overflow-auto border border-gray-200 rounded-lg bg-gray-50">
        <svg
          width={maxX - minX}
          height={maxY - minY}
          viewBox={`${minX} ${minY} ${maxX - minX} ${maxY - minY}`}
          className="min-w-full min-h-96"
        >
          {/* Connections */}
          {showConnections && connections.map((conn, index) => {
            const isHighlighted = selectedStep === conn.from || selectedStep === conn.to ||
                                hoveredStep === conn.from || hoveredStep === conn.to;
            
            return (
              <g key={index}>
                {/* Connection Line */}
                <path
                  d={`M ${conn.fromX} ${conn.fromY} 
                      C ${conn.fromX + 50} ${conn.fromY} 
                        ${conn.toX - 50} ${conn.toY} 
                        ${conn.toX} ${conn.toY}`}
                  stroke={isHighlighted ? '#3B82F6' : '#9CA3AF'}
                  strokeWidth={isHighlighted ? 3 : 2}
                  fill="none"
                  markerEnd="url(#arrowhead)"
                  className="transition-all duration-200"
                />
                
                {/* Arrow Marker */}
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill={isHighlighted ? '#3B82F6' : '#9CA3AF'}
                    />
                  </marker>
                </defs>
              </g>
            );
          })}

          {/* Nodes */}
          {flowNodes.map(node => {
            const isSelected = selectedStep === node.stepId;
            const isHovered = hoveredStep === node.stepId;
            const isCurrent = currentStep === node.stepId;
            
            return (
              <g key={node.stepId}>
                {/* Node Background */}
                <rect
                  x={node.x}
                  y={node.y}
                  width={node.width}
                  height={node.height}
                  rx="8"
                  className={`cursor-pointer transition-all duration-200 border-2 ${getStatusColor(node.status)} ${
                    isSelected ? 'ring-4 ring-blue-300' : ''
                  } ${isHovered ? 'shadow-lg' : 'shadow'}`}
                  onClick={() => handleStepClick(node.stepId)}
                  onMouseEnter={() => setHoveredStep(node.stepId)}
                  onMouseLeave={() => setHoveredStep(null)}
                />

                {/* Current Step Indicator */}
                {isCurrent && (
                  <circle
                    cx={node.x + node.width - 8}
                    cy={node.y + 8}
                    r="4"
                    fill="#3B82F6"
                    className="animate-pulse"
                  />
                )}

                {/* Node Content */}
                <foreignObject
                  x={node.x + 8}
                  y={node.y + 8}
                  width={node.width - 16}
                  height={node.height - 16}
                  className="pointer-events-none"
                >
                  <div className="flex flex-col items-center justify-center h-full text-center">
                    <div className="text-lg mb-1">
                      {getStatusIcon(node.status)}
                    </div>
                    {showLabels && (
                      <div className="text-xs font-medium truncate w-full">
                        {node.stepName || node.stepId}
                      </div>
                    )}
                  </div>
                </foreignObject>
              </g>
            );
          })}
        </svg>
      </div>

      {/* Selected Step Details */}
      {selectedStep && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          {(() => {
            const step = flowNodes.find(n => n.stepId === selectedStep);
            if (!step) return null;

            return (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-blue-900">
                    {step.stepName || step.stepId}
                  </h4>
                  <span className={`px-2 py-1 rounded text-xs font-medium capitalize ${
                    getStatusColor(step.status).replace('text-white', 'text-blue-900')
                  }`}>
                    {step.status.replace('_', ' ')}
                  </span>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Level:</span>
                    <span className="ml-2 text-blue-700">{step.level}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Dependencies:</span>
                    <span className="ml-2 text-blue-700">
                      {step.dependencies.length || 'None'}
                    </span>
                  </div>
                  {step.duration && (
                    <div>
                      <span className="font-medium text-blue-800">Duration:</span>
                      <span className="ml-2 text-blue-700">
                        {Math.round(step.duration / 1000)}s
                      </span>
                    </div>
                  )}
                  {step.startedAt && (
                    <div>
                      <span className="font-medium text-blue-800">Started:</span>
                      <span className="ml-2 text-blue-700">
                        {new Date(step.startedAt).toLocaleTimeString()}
                      </span>
                    </div>
                  )}
                </div>

                {step.dependencies.length > 0 && (
                  <div className="mt-3">
                    <span className="font-medium text-blue-800">Depends on:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {step.dependencies.map(dep => (
                        <button
                          key={dep}
                          onClick={() => setSelectedStep(dep)}
                          className="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs rounded"
                        >
                          {flowNodes.find(n => n.stepId === dep)?.stepName || dep}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {step.error && (
                  <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    {step.error}
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      )}

      {/* Legend */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded"></div>
            <span>Running</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded"></div>
            <span>Failed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-purple-500 rounded"></div>
            <span>Review</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gray-200 rounded"></div>
            <span>Pending</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default WorkflowFlowChart;
