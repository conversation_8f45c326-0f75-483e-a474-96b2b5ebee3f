/**
 * Cost Tracker Component
 * Real-time AI cost tracking and budget monitoring
 */

'use client';

import React, { useState, useEffect } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface CostData {
  totalCost: number;
  costByStep: Record<string, number>;
  costByAgent: Record<string, number>;
  tokenUsage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  budget?: {
    limit: number;
    used: number;
    remaining: number;
    alertThreshold: number;
  };
}

interface CostTrackerProps {
  executionId?: string;
  steps: StepProgressInfo[];
  budget?: number;
  alertThreshold?: number;
  showBreakdown?: boolean;
  showTokens?: boolean;
  showAlerts?: boolean;
  updateInterval?: number;
  className?: string;
}

export function CostTracker({
  executionId,
  steps,
  budget,
  alertThreshold = 0.8,
  showBreakdown = true,
  showTokens = true,
  showAlerts = true,
  updateInterval = 5000,
  className = ''
}: CostTrackerProps) {
  const [costData, setCostData] = useState<CostData>({
    totalCost: 0,
    costByStep: {},
    costByAgent: {},
    tokenUsage: {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0
    }
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch cost data
  const fetchCostData = async () => {
    if (!executionId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/workflow/costs/${executionId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch cost data');
      }

      const data = await response.json();
      
      const newCostData: CostData = {
        totalCost: data.totalCost || 0,
        costByStep: data.costByStep || {},
        costByAgent: data.costByAgent || {},
        tokenUsage: {
          inputTokens: data.tokenUsage?.inputTokens || 0,
          outputTokens: data.tokenUsage?.outputTokens || 0,
          totalTokens: data.tokenUsage?.totalTokens || 0
        }
      };

      // Add budget information if provided
      if (budget) {
        newCostData.budget = {
          limit: budget,
          used: newCostData.totalCost,
          remaining: Math.max(budget - newCostData.totalCost, 0),
          alertThreshold
        };
      }

      setCostData(newCostData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  // Update cost data periodically
  useEffect(() => {
    fetchCostData();
    
    const timer = setInterval(fetchCostData, updateInterval);
    return () => clearInterval(timer);
  }, [executionId, updateInterval]);

  const formatCost = (cost: number) => {
    if (cost < 0.01) return `$${(cost * 100).toFixed(2)}¢`;
    return `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`;
    return tokens.toString();
  };

  const getBudgetStatus = () => {
    if (!costData.budget) return null;
    
    const usagePercent = (costData.budget.used / costData.budget.limit) * 100;
    
    if (usagePercent >= 100) return { status: 'exceeded', color: 'red' };
    if (usagePercent >= costData.budget.alertThreshold * 100) return { status: 'warning', color: 'yellow' };
    return { status: 'normal', color: 'green' };
  };

  const budgetStatus = getBudgetStatus();

  const getStepCosts = () => {
    return steps.map(step => ({
      ...step,
      cost: costData.costByStep[step.stepId] || 0
    })).sort((a, b) => b.cost - a.cost);
  };

  const getAgentCosts = () => {
    return Object.entries(costData.costByAgent)
      .map(([agent, cost]) => ({ agent, cost }))
      .sort((a, b) => b.cost - a.cost);
  };

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="text-red-700">
          <div className="font-medium">Cost tracking error</div>
          <div className="text-sm mt-1">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Cost Tracker</h3>
          <p className="text-sm text-gray-600">Real-time AI usage costs</p>
        </div>
        
        {isLoading && (
          <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        )}
      </div>

      {/* Budget Alert */}
      {showAlerts && budgetStatus && budgetStatus.status !== 'normal' && (
        <div className={`mb-6 p-4 rounded-lg border ${
          budgetStatus.status === 'exceeded' 
            ? 'bg-red-50 border-red-200' 
            : 'bg-yellow-50 border-yellow-200'
        }`}>
          <div className={`font-medium ${
            budgetStatus.status === 'exceeded' ? 'text-red-700' : 'text-yellow-700'
          }`}>
            {budgetStatus.status === 'exceeded' ? '⚠️ Budget Exceeded' : '⚠️ Budget Alert'}
          </div>
          <div className={`text-sm mt-1 ${
            budgetStatus.status === 'exceeded' ? 'text-red-600' : 'text-yellow-600'
          }`}>
            {budgetStatus.status === 'exceeded' 
              ? `Costs have exceeded the budget limit of ${formatCost(costData.budget!.limit)}`
              : `Costs are approaching the budget limit (${Math.round((costData.budget!.used / costData.budget!.limit) * 100)}%)`
            }
          </div>
        </div>
      )}

      {/* Main Cost Display */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {/* Total Cost */}
        <div className="text-center">
          <div className="text-3xl font-bold text-blue-600">
            {formatCost(costData.totalCost)}
          </div>
          <div className="text-sm text-gray-600">Total Cost</div>
        </div>

        {/* Token Usage */}
        {showTokens && (
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600">
              {formatTokens(costData.tokenUsage.totalTokens)}
            </div>
            <div className="text-sm text-gray-600">Total Tokens</div>
            <div className="text-xs text-gray-500 mt-1">
              {formatTokens(costData.tokenUsage.inputTokens)} in • {formatTokens(costData.tokenUsage.outputTokens)} out
            </div>
          </div>
        )}

        {/* Budget Status */}
        {costData.budget && (
          <div className="text-center">
            <div className={`text-3xl font-bold ${
              budgetStatus?.status === 'exceeded' ? 'text-red-600' :
              budgetStatus?.status === 'warning' ? 'text-yellow-600' :
              'text-green-600'
            }`}>
              {formatCost(costData.budget.remaining)}
            </div>
            <div className="text-sm text-gray-600">Remaining</div>
            <div className="text-xs text-gray-500 mt-1">
              of {formatCost(costData.budget.limit)} budget
            </div>
          </div>
        )}
      </div>

      {/* Budget Progress Bar */}
      {costData.budget && (
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Budget Usage</span>
            <span>{Math.round((costData.budget.used / costData.budget.limit) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${
                budgetStatus?.status === 'exceeded' ? 'bg-red-500' :
                budgetStatus?.status === 'warning' ? 'bg-yellow-500' :
                'bg-green-500'
              }`}
              style={{ 
                width: `${Math.min((costData.budget.used / costData.budget.limit) * 100, 100)}%` 
              }}
            />
          </div>
        </div>
      )}

      {/* Cost Breakdown */}
      {showBreakdown && (
        <div className="space-y-6">
          {/* Cost by Step */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Cost by Step</h4>
            <div className="space-y-2">
              {getStepCosts().slice(0, 5).map(step => (
                <div key={step.stepId} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      step.status === 'completed' ? 'bg-green-500' :
                      step.status === 'running' ? 'bg-blue-500' :
                      step.status === 'failed' ? 'bg-red-500' :
                      'bg-gray-300'
                    }`} />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {step.stepName || step.stepId}
                      </div>
                      <div className="text-xs text-gray-500 capitalize">
                        {step.status.replace('_', ' ')}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-gray-900">
                    {formatCost(step.cost)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Cost by Agent */}
          {getAgentCosts().length > 0 && (
            <div>
              <h4 className="text-md font-medium text-gray-900 mb-3">Cost by Agent</h4>
              <div className="space-y-2">
                {getAgentCosts().slice(0, 5).map(({ agent, cost }) => (
                  <div key={agent} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div className="text-sm font-medium text-gray-900 capitalize">
                      {agent.replace('_', ' ')}
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatCost(cost)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Last Updated */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>Last updated: {new Date().toLocaleTimeString()}</span>
          <button
            onClick={fetchCostData}
            className="text-blue-600 hover:text-blue-700"
            disabled={isLoading}
          >
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
}

export default CostTracker;
