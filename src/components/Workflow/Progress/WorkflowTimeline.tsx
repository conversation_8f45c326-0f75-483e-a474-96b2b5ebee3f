/**
 * Workflow Timeline Component
 * Interactive timeline visualization for workflow progress
 */

'use client';

import React, { useState, useMemo } from 'react';
import { StepProgressInfo, WorkflowEvent } from '../../../hooks/useWorkflowProgress';

interface TimelineStep extends StepProgressInfo {
  estimatedDuration?: number;
  dependencies?: string[];
  position?: number;
}

interface WorkflowTimelineProps {
  steps: TimelineStep[];
  events: WorkflowEvent[];
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  showEstimates?: boolean;
  showDependencies?: boolean;
  className?: string;
}

export function WorkflowTimeline({
  steps,
  events,
  currentStep,
  onStepClick,
  showEstimates = true,
  showDependencies = false,
  className = ''
}: WorkflowTimelineProps) {
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'timeline' | 'gantt'>('timeline');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-500 border-green-600';
      case 'running': return 'bg-blue-500 border-blue-600 animate-pulse';
      case 'failed': return 'bg-red-500 border-red-600';
      case 'waiting_review': return 'bg-purple-500 border-purple-600';
      case 'waiting_approval': return 'bg-orange-500 border-orange-600';
      case 'pending': return 'bg-gray-300 border-gray-400';
      default: return 'bg-gray-300 border-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✓';
      case 'running': return '⟳';
      case 'failed': return '✗';
      case 'waiting_review': return '👥';
      case 'waiting_approval': return '⏳';
      case 'pending': return '○';
      default: return '○';
    }
  };

  const formatDuration = (ms?: number) => {
    if (!ms) return '';
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
  };

  const calculateProgress = () => {
    const completedSteps = steps.filter(step => step.status === 'completed').length;
    return steps.length > 0 ? (completedSteps / steps.length) * 100 : 0;
  };

  const getStepEvents = (stepId: string) => {
    return events.filter(event => 
      event.data?.stepId === stepId || 
      event.type.includes(stepId)
    );
  };

  const timelineSteps = useMemo(() => {
    return steps.map((step, index) => ({
      ...step,
      position: index,
      isActive: step.stepId === currentStep,
      isSelected: step.stepId === selectedStep,
      events: getStepEvents(step.stepId)
    }));
  }, [steps, currentStep, selectedStep, events]);

  const handleStepClick = (stepId: string) => {
    setSelectedStep(selectedStep === stepId ? null : stepId);
    onStepClick?.(stepId);
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Workflow Timeline</h3>
          <p className="text-sm text-gray-600">
            {Math.round(calculateProgress())}% complete • {steps.length} steps
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('timeline')}
            className={`px-3 py-1 text-sm rounded ${
              viewMode === 'timeline' 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Timeline
          </button>
          <button
            onClick={() => setViewMode('gantt')}
            className={`px-3 py-1 text-sm rounded ${
              viewMode === 'gantt' 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            Gantt
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-500"
            style={{ width: `${calculateProgress()}%` }}
          />
        </div>
      </div>

      {/* Timeline View */}
      {viewMode === 'timeline' && (
        <div className="relative">
          {/* Timeline Line */}
          <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-300" />
          
          {/* Steps */}
          <div className="space-y-4">
            {timelineSteps.map((step, index) => (
              <div key={step.stepId} className="relative">
                {/* Timeline Node */}
                <div 
                  className={`absolute left-4 w-4 h-4 rounded-full border-2 cursor-pointer transition-all duration-200 ${
                    getStatusColor(step.status)
                  } ${step.isActive ? 'ring-2 ring-blue-300' : ''}`}
                  onClick={() => handleStepClick(step.stepId)}
                  title={`${step.stepName || step.stepId} - ${step.status}`}
                >
                  <span className="absolute inset-0 flex items-center justify-center text-xs text-white font-bold">
                    {getStatusIcon(step.status)}
                  </span>
                </div>

                {/* Step Content */}
                <div className="ml-12 pb-4">
                  <div 
                    className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 ${
                      step.isSelected 
                        ? 'border-blue-300 bg-blue-50' 
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                    onClick={() => handleStepClick(step.stepId)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">
                          {step.stepName || step.stepId}
                        </h4>
                        <p className="text-sm text-gray-600 capitalize">
                          Status: {step.status.replace('_', ' ')}
                        </p>
                      </div>
                      
                      <div className="text-right text-sm text-gray-500">
                        {step.duration && (
                          <div>Duration: {formatDuration(step.duration)}</div>
                        )}
                        {showEstimates && step.estimatedDuration && (
                          <div>Est: {formatDuration(step.estimatedDuration)}</div>
                        )}
                      </div>
                    </div>

                    {/* Expanded Details */}
                    {step.isSelected && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Started:</span>
                            <div className="text-gray-600">
                              {step.startedAt ? new Date(step.startedAt).toLocaleString() : 'Not started'}
                            </div>
                          </div>
                          <div>
                            <span className="font-medium">Completed:</span>
                            <div className="text-gray-600">
                              {step.completedAt ? new Date(step.completedAt).toLocaleString() : 'Not completed'}
                            </div>
                          </div>
                        </div>

                        {step.error && (
                          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                            <p className="text-sm text-red-700">{step.error}</p>
                          </div>
                        )}

                        {showDependencies && step.dependencies && step.dependencies.length > 0 && (
                          <div className="mt-3">
                            <span className="font-medium text-sm">Dependencies:</span>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {step.dependencies.map(dep => (
                                <span key={dep} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                                  {dep}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}

                        {step.events.length > 0 && (
                          <div className="mt-3">
                            <span className="font-medium text-sm">Recent Events:</span>
                            <div className="mt-1 space-y-1">
                              {step.events.slice(-3).map(event => (
                                <div key={event.id} className="text-xs text-gray-600">
                                  <span className="font-medium">{event.type}</span>
                                  <span className="ml-2">{new Date(event.timestamp).toLocaleTimeString()}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Gantt View */}
      {viewMode === 'gantt' && (
        <div className="space-y-2">
          <div className="text-sm text-gray-600 mb-4">
            Gantt chart view showing step timing and dependencies
          </div>
          {timelineSteps.map((step, index) => (
            <div key={step.stepId} className="flex items-center space-x-4 p-2 hover:bg-gray-50 rounded">
              <div className="w-32 text-sm font-medium text-gray-900 truncate">
                {step.stepName || step.stepId}
              </div>
              <div className="flex-1 relative h-6 bg-gray-100 rounded">
                {step.startedAt && (
                  <div 
                    className={`absolute left-0 top-0 h-full rounded ${getStatusColor(step.status).split(' ')[0]} opacity-75`}
                    style={{ 
                      width: step.duration ? `${Math.min((step.duration / 300000) * 100, 100)}%` : '10%'
                    }}
                  />
                )}
              </div>
              <div className="w-20 text-xs text-gray-500 text-right">
                {step.duration ? formatDuration(step.duration) : 'Pending'}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default WorkflowTimeline;
