/**
 * Advanced Progress Indicator Component
 * Enhanced progress bars with animations and detailed metrics
 */

'use client';

import React, { useState, useEffect } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface ProgressMetrics {
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  runningSteps: number;
  pendingSteps: number;
  overallProgress: number;
  estimatedTimeRemaining?: number;
  averageStepDuration?: number;
}

interface ProgressIndicatorProps {
  steps: StepProgressInfo[];
  currentStep?: string;
  showMetrics?: boolean;
  showEstimates?: boolean;
  showStepBreakdown?: boolean;
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'linear' | 'circular' | 'stepped';
  className?: string;
}

export function ProgressIndicator({
  steps,
  currentStep,
  showMetrics = true,
  showEstimates = false,
  showStepBreakdown = false,
  animated = true,
  size = 'md',
  variant = 'linear',
  className = ''
}: ProgressIndicatorProps) {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  const metrics: ProgressMetrics = {
    totalSteps: steps.length,
    completedSteps: steps.filter(s => s.status === 'completed').length,
    failedSteps: steps.filter(s => s.status === 'failed').length,
    runningSteps: steps.filter(s => s.status === 'running').length,
    pendingSteps: steps.filter(s => s.status === 'pending').length,
    overallProgress: steps.length > 0 ? (steps.filter(s => s.status === 'completed').length / steps.length) * 100 : 0,
    averageStepDuration: calculateAverageStepDuration(steps),
    estimatedTimeRemaining: calculateEstimatedTimeRemaining(steps)
  };

  function calculateAverageStepDuration(steps: StepProgressInfo[]): number {
    const completedSteps = steps.filter(s => s.status === 'completed' && s.duration);
    if (completedSteps.length === 0) return 0;
    
    const totalDuration = completedSteps.reduce((sum, step) => sum + (step.duration || 0), 0);
    return totalDuration / completedSteps.length;
  }

  function calculateEstimatedTimeRemaining(steps: StepProgressInfo[]): number {
    const pendingSteps = steps.filter(s => s.status === 'pending').length;
    const avgDuration = calculateAverageStepDuration(steps);
    return pendingSteps * avgDuration;
  }

  const formatDuration = (ms: number) => {
    if (ms < 60000) return `${Math.round(ms / 1000)}s`;
    if (ms < 3600000) return `${Math.round(ms / 60000)}m`;
    return `${Math.round(ms / 3600000)}h`;
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm': return 'h-2 text-sm';
      case 'lg': return 'h-6 text-lg';
      default: return 'h-4 text-base';
    }
  };

  const getProgressColor = () => {
    if (metrics.failedSteps > 0) return 'bg-red-500';
    if (metrics.runningSteps > 0) return 'bg-blue-500';
    if (metrics.overallProgress === 100) return 'bg-green-500';
    return 'bg-blue-500';
  };

  // Animate progress on mount and updates
  useEffect(() => {
    setIsVisible(true);
    const timer = setTimeout(() => {
      setAnimatedProgress(metrics.overallProgress);
    }, 100);
    return () => clearTimeout(timer);
  }, [metrics.overallProgress]);

  // Linear Progress Bar
  const LinearProgress = () => (
    <div className="space-y-3">
      {/* Main Progress Bar */}
      <div className="relative">
        <div className={`w-full bg-gray-200 rounded-full ${getSizeClasses().split(' ')[0]} overflow-hidden`}>
          <div 
            className={`${getProgressColor()} ${getSizeClasses().split(' ')[0]} rounded-full transition-all duration-1000 ease-out ${
              animated ? 'transform' : ''
            }`}
            style={{ 
              width: animated ? `${animatedProgress}%` : `${metrics.overallProgress}%`,
              transform: isVisible ? 'translateX(0)' : 'translateX(-100%)'
            }}
          />
          
          {/* Running Step Indicator */}
          {metrics.runningSteps > 0 && (
            <div 
              className="absolute top-0 h-full w-2 bg-white opacity-75 animate-pulse"
              style={{ left: `${metrics.overallProgress}%` }}
            />
          )}
        </div>
        
        {/* Percentage Label */}
        <div className="absolute right-0 top-0 -mt-6">
          <span className={`font-bold text-gray-900 ${getSizeClasses().split(' ')[1]}`}>
            {Math.round(metrics.overallProgress)}%
          </span>
        </div>
      </div>

      {/* Step Breakdown Bar */}
      {showStepBreakdown && (
        <div className="flex w-full h-2 bg-gray-200 rounded-full overflow-hidden">
          {steps.map((step, index) => {
            const stepWidth = (1 / steps.length) * 100;
            let stepColor = 'bg-gray-300';
            
            switch (step.status) {
              case 'completed': stepColor = 'bg-green-500'; break;
              case 'running': stepColor = 'bg-blue-500 animate-pulse'; break;
              case 'failed': stepColor = 'bg-red-500'; break;
              case 'waiting_review': stepColor = 'bg-purple-500'; break;
              case 'waiting_approval': stepColor = 'bg-orange-500'; break;
            }
            
            return (
              <div
                key={step.stepId}
                className={`${stepColor} transition-all duration-500`}
                style={{ width: `${stepWidth}%` }}
                title={`${step.stepName || step.stepId}: ${step.status}`}
              />
            );
          })}
        </div>
      )}
    </div>
  );

  // Circular Progress
  const CircularProgress = () => {
    const radius = size === 'sm' ? 20 : size === 'lg' ? 40 : 30;
    const circumference = 2 * Math.PI * radius;
    const strokeDashoffset = circumference - (animatedProgress / 100) * circumference;

    return (
      <div className="relative inline-flex items-center justify-center">
        <svg 
          className={`transform -rotate-90 ${
            size === 'sm' ? 'w-12 h-12' : size === 'lg' ? 'w-24 h-24' : 'w-16 h-16'
          }`}
        >
          {/* Background Circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="4"
            fill="transparent"
            className="text-gray-200"
          />
          
          {/* Progress Circle */}
          <circle
            cx="50%"
            cy="50%"
            r={radius}
            stroke="currentColor"
            strokeWidth="4"
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={animated ? strokeDashoffset : circumference - (metrics.overallProgress / 100) * circumference}
            className={`transition-all duration-1000 ease-out ${
              metrics.failedSteps > 0 ? 'text-red-500' :
              metrics.runningSteps > 0 ? 'text-blue-500' :
              metrics.overallProgress === 100 ? 'text-green-500' :
              'text-blue-500'
            }`}
            strokeLinecap="round"
          />
        </svg>
        
        {/* Center Text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className={`font-bold text-gray-900 ${getSizeClasses().split(' ')[1]}`}>
            {Math.round(metrics.overallProgress)}%
          </span>
        </div>
      </div>
    );
  };

  // Stepped Progress
  const SteppedProgress = () => (
    <div className="flex items-center space-x-2">
      {steps.map((step, index) => {
        const isActive = step.stepId === currentStep;
        const isCompleted = step.status === 'completed';
        const isFailed = step.status === 'failed';
        const isRunning = step.status === 'running';
        
        return (
          <React.Fragment key={step.stepId}>
            <div 
              className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-bold transition-all duration-300 ${
                isFailed ? 'bg-red-500 border-red-600 text-white' :
                isCompleted ? 'bg-green-500 border-green-600 text-white' :
                isRunning ? 'bg-blue-500 border-blue-600 text-white animate-pulse' :
                isActive ? 'bg-blue-100 border-blue-500 text-blue-700' :
                'bg-gray-100 border-gray-300 text-gray-500'
              }`}
              title={`${step.stepName || step.stepId}: ${step.status}`}
            >
              {isCompleted ? '✓' : isFailed ? '✗' : index + 1}
            </div>
            
            {index < steps.length - 1 && (
              <div className={`flex-1 h-1 rounded ${
                isCompleted ? 'bg-green-500' : 'bg-gray-200'
              }`} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );

  return (
    <div className={`${className}`}>
      {/* Progress Visualization */}
      <div className="mb-4">
        {variant === 'circular' && <CircularProgress />}
        {variant === 'stepped' && <SteppedProgress />}
        {variant === 'linear' && <LinearProgress />}
      </div>

      {/* Metrics */}
      {showMetrics && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
          <div className="text-center">
            <div className="font-bold text-green-600">{metrics.completedSteps}</div>
            <div className="text-gray-600">Completed</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-blue-600">{metrics.runningSteps}</div>
            <div className="text-gray-600">Running</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-red-600">{metrics.failedSteps}</div>
            <div className="text-gray-600">Failed</div>
          </div>
          <div className="text-center">
            <div className="font-bold text-gray-600">{metrics.pendingSteps}</div>
            <div className="text-gray-600">Pending</div>
          </div>
        </div>
      )}

      {/* Time Estimates */}
      {showEstimates && (metrics.averageStepDuration > 0 || metrics.estimatedTimeRemaining > 0) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
            {metrics.averageStepDuration > 0 && (
              <div>
                <span className="font-medium text-gray-700">Avg Step Duration:</span>
                <span className="ml-2 text-gray-600">{formatDuration(metrics.averageStepDuration)}</span>
              </div>
            )}
            {metrics.estimatedTimeRemaining > 0 && (
              <div>
                <span className="font-medium text-gray-700">Est. Time Remaining:</span>
                <span className="ml-2 text-gray-600">{formatDuration(metrics.estimatedTimeRemaining)}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default ProgressIndicator;
