/**
 * Dependency Graph Component
 * Visual representation of step dependencies and workflow structure
 */

'use client';

import React, { useState, useMemo } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface GraphNode extends StepProgressInfo {
  x: number;
  y: number;
  level: number;
  dependencies: string[];
  dependents: string[];
}

interface GraphEdge {
  from: string;
  to: string;
  fromX: number;
  fromY: number;
  toX: number;
  toY: number;
  type: 'dependency' | 'flow';
}

interface DependencyGraphProps {
  steps: StepProgressInfo[];
  dependencies: Record<string, string[]>;
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  layout?: 'hierarchical' | 'circular' | 'force';
  showLabels?: boolean;
  showCriticalPath?: boolean;
  className?: string;
}

export function DependencyGraph({
  steps,
  dependencies,
  currentStep,
  onStepClick,
  layout = 'hierarchical',
  showLabels = true,
  showCriticalPath = false,
  className = ''
}: DependencyGraphProps) {
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [hoveredNode, setHoveredNode] = useState<string | null>(null);
  const [highlightPath, setHighlightPath] = useState<string[]>([]);

  const nodeSize = 40;
  const levelSpacing = 120;
  const nodeSpacing = 80;

  // Calculate graph layout
  const { nodes, edges } = useMemo(() => {
    const nodeMap = new Map<string, GraphNode>();
    const edgeList: GraphEdge[] = [];

    // Initialize nodes with dependency information
    steps.forEach(step => {
      const stepDeps = dependencies[step.stepId] || [];
      const stepDependents = Object.entries(dependencies)
        .filter(([_, deps]) => deps.includes(step.stepId))
        .map(([stepId]) => stepId);

      nodeMap.set(step.stepId, {
        ...step,
        x: 0,
        y: 0,
        level: 0,
        dependencies: stepDeps,
        dependents: stepDependents
      });
    });

    // Calculate levels for hierarchical layout
    const calculateLevel = (stepId: string, visited = new Set<string>()): number => {
      if (visited.has(stepId)) return 0; // Circular dependency protection
      
      const node = nodeMap.get(stepId);
      if (!node || node.level > 0) return node?.level || 0;

      visited.add(stepId);
      
      if (node.dependencies.length === 0) {
        node.level = 1;
      } else {
        const maxDepLevel = Math.max(...node.dependencies.map(dep => calculateLevel(dep, visited)));
        node.level = maxDepLevel + 1;
      }

      visited.delete(stepId);
      return node.level;
    };

    // Calculate positions based on layout
    if (layout === 'hierarchical') {
      // Calculate levels
      steps.forEach(step => calculateLevel(step.stepId));

      // Group by levels
      const levels: Record<number, string[]> = {};
      nodeMap.forEach(node => {
        if (!levels[node.level]) levels[node.level] = [];
        levels[node.level].push(node.stepId);
      });

      // Position nodes
      Object.entries(levels).forEach(([levelStr, stepIds]) => {
        const level = parseInt(levelStr);
        stepIds.forEach((stepId, index) => {
          const node = nodeMap.get(stepId)!;
          node.x = (level - 1) * levelSpacing + 50;
          node.y = (index - (stepIds.length - 1) / 2) * nodeSpacing + 200;
        });
      });
    } else if (layout === 'circular') {
      // Circular layout
      const radius = Math.max(100, steps.length * 15);
      const angleStep = (2 * Math.PI) / steps.length;
      
      steps.forEach((step, index) => {
        const angle = index * angleStep;
        const node = nodeMap.get(step.stepId)!;
        node.x = Math.cos(angle) * radius + 250;
        node.y = Math.sin(angle) * radius + 200;
      });
    } else {
      // Force-directed layout (simplified)
      steps.forEach((step, index) => {
        const node = nodeMap.get(step.stepId)!;
        node.x = (index % 5) * 100 + 50;
        node.y = Math.floor(index / 5) * 100 + 50;
      });
    }

    // Create edges
    nodeMap.forEach(node => {
      node.dependencies.forEach(depId => {
        const depNode = nodeMap.get(depId);
        if (depNode) {
          edgeList.push({
            from: depId,
            to: node.stepId,
            fromX: depNode.x + nodeSize / 2,
            fromY: depNode.y + nodeSize / 2,
            toX: node.x + nodeSize / 2,
            toY: node.y + nodeSize / 2,
            type: 'dependency'
          });
        }
      });
    });

    return {
      nodes: Array.from(nodeMap.values()),
      edges: edgeList
    };
  }, [steps, dependencies, layout]);

  // Calculate critical path
  const criticalPath = useMemo(() => {
    if (!showCriticalPath) return [];

    // Simple critical path calculation based on longest dependency chain
    const visited = new Set<string>();
    const path: string[] = [];

    const findLongestPath = (nodeId: string, currentPath: string[]): string[] => {
      if (visited.has(nodeId)) return currentPath;
      
      visited.add(nodeId);
      const node = nodes.find(n => n.stepId === nodeId);
      if (!node) return currentPath;

      const newPath = [...currentPath, nodeId];
      
      if (node.dependents.length === 0) {
        return newPath;
      }

      let longestPath = newPath;
      node.dependents.forEach(dependent => {
        const depPath = findLongestPath(dependent, newPath);
        if (depPath.length > longestPath.length) {
          longestPath = depPath;
        }
      });

      return longestPath;
    };

    // Find root nodes (no dependencies)
    const rootNodes = nodes.filter(n => n.dependencies.length === 0);
    let longestOverallPath: string[] = [];

    rootNodes.forEach(root => {
      visited.clear();
      const path = findLongestPath(root.stepId, []);
      if (path.length > longestOverallPath.length) {
        longestOverallPath = path;
      }
    });

    return longestOverallPath;
  }, [nodes, showCriticalPath]);

  const getNodeColor = (node: GraphNode) => {
    const isSelected = selectedNode === node.stepId;
    const isHovered = hoveredNode === node.stepId;
    const isCurrent = currentStep === node.stepId;
    const isInCriticalPath = criticalPath.includes(node.stepId);
    const isHighlighted = highlightPath.includes(node.stepId);

    let baseColor = '';
    switch (node.status) {
      case 'completed': baseColor = 'bg-green-500'; break;
      case 'running': baseColor = 'bg-blue-500'; break;
      case 'failed': baseColor = 'bg-red-500'; break;
      case 'waiting_review': baseColor = 'bg-purple-500'; break;
      case 'waiting_approval': baseColor = 'bg-orange-500'; break;
      default: baseColor = 'bg-gray-300'; break;
    }

    const modifiers = [];
    if (isSelected) modifiers.push('ring-4 ring-blue-300');
    if (isHovered) modifiers.push('shadow-lg scale-110');
    if (isCurrent) modifiers.push('ring-2 ring-yellow-400');
    if (isInCriticalPath) modifiers.push('ring-2 ring-red-400');
    if (isHighlighted) modifiers.push('ring-2 ring-purple-400');

    return `${baseColor} ${modifiers.join(' ')} transition-all duration-200`;
  };

  const getEdgeColor = (edge: GraphEdge) => {
    const isHighlighted = highlightPath.includes(edge.from) && highlightPath.includes(edge.to);
    const isInCriticalPath = criticalPath.includes(edge.from) && criticalPath.includes(edge.to);
    const isConnectedToSelected = selectedNode === edge.from || selectedNode === edge.to;

    if (isInCriticalPath) return '#EF4444'; // red
    if (isHighlighted) return '#8B5CF6'; // purple
    if (isConnectedToSelected) return '#3B82F6'; // blue
    return '#9CA3AF'; // gray
  };

  const handleNodeClick = (nodeId: string) => {
    setSelectedNode(selectedNode === nodeId ? null : nodeId);
    onStepClick?.(nodeId);
  };

  const handleNodeHover = (nodeId: string | null) => {
    setHoveredNode(nodeId);
    
    if (nodeId) {
      const node = nodes.find(n => n.stepId === nodeId);
      if (node) {
        // Highlight dependency path
        const path = new Set([nodeId, ...node.dependencies, ...node.dependents]);
        setHighlightPath(Array.from(path));
      }
    } else {
      setHighlightPath([]);
    }
  };

  // Calculate SVG dimensions
  const maxX = Math.max(...nodes.map(n => n.x + nodeSize)) + 50;
  const maxY = Math.max(...nodes.map(n => n.y + nodeSize)) + 50;
  const minX = Math.min(...nodes.map(n => n.x)) - 50;
  const minY = Math.min(...nodes.map(n => n.y)) - 50;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Dependency Graph</h3>
          <p className="text-sm text-gray-600">
            Visual representation of step dependencies
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={layout}
            onChange={(e) => setLayout(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="hierarchical">Hierarchical</option>
            <option value="circular">Circular</option>
            <option value="force">Force</option>
          </select>
          
          <button
            onClick={() => setShowCriticalPath(!showCriticalPath)}
            className={`px-3 py-1 text-sm rounded ${
              showCriticalPath 
                ? 'bg-red-100 text-red-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Critical Path
          </button>
        </div>
      </div>

      {/* Graph */}
      <div className="overflow-auto border border-gray-200 rounded-lg bg-gray-50">
        <svg
          width={maxX - minX}
          height={maxY - minY}
          viewBox={`${minX} ${minY} ${maxX - minX} ${maxY - minY}`}
          className="min-w-full min-h-96"
        >
          {/* Edges */}
          {edges.map((edge, index) => (
            <g key={index}>
              <defs>
                <marker
                  id={`arrowhead-${index}`}
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill={getEdgeColor(edge)}
                  />
                </marker>
              </defs>
              
              <line
                x1={edge.fromX}
                y1={edge.fromY}
                x2={edge.toX}
                y2={edge.toY}
                stroke={getEdgeColor(edge)}
                strokeWidth="2"
                markerEnd={`url(#arrowhead-${index})`}
                className="transition-all duration-200"
              />
            </g>
          ))}

          {/* Nodes */}
          {nodes.map(node => (
            <g key={node.stepId}>
              {/* Node Circle */}
              <circle
                cx={node.x + nodeSize / 2}
                cy={node.y + nodeSize / 2}
                r={nodeSize / 2}
                className={`cursor-pointer ${getNodeColor(node)}`}
                onClick={() => handleNodeClick(node.stepId)}
                onMouseEnter={() => handleNodeHover(node.stepId)}
                onMouseLeave={() => handleNodeHover(null)}
              />

              {/* Node Label */}
              {showLabels && (
                <text
                  x={node.x + nodeSize / 2}
                  y={node.y + nodeSize + 15}
                  textAnchor="middle"
                  className="text-xs font-medium fill-gray-700 pointer-events-none"
                >
                  {node.stepName || node.stepId}
                </text>
              )}

              {/* Status Icon */}
              <text
                x={node.x + nodeSize / 2}
                y={node.y + nodeSize / 2 + 4}
                textAnchor="middle"
                className="text-sm font-bold fill-white pointer-events-none"
              >
                {node.status === 'completed' ? '✓' :
                 node.status === 'failed' ? '✗' :
                 node.status === 'running' ? '⟳' : '○'}
              </text>
            </g>
          ))}
        </svg>
      </div>

      {/* Selected Node Details */}
      {selectedNode && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          {(() => {
            const node = nodes.find(n => n.stepId === selectedNode);
            if (!node) return null;

            return (
              <div>
                <h4 className="font-medium text-blue-900 mb-3">
                  {node.stepName || node.stepId}
                </h4>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Status:</span>
                    <span className="ml-2 text-blue-700 capitalize">
                      {node.status.replace('_', ' ')}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Level:</span>
                    <span className="ml-2 text-blue-700">{node.level}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Dependencies:</span>
                    <span className="ml-2 text-blue-700">{node.dependencies.length}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Dependents:</span>
                    <span className="ml-2 text-blue-700">{node.dependents.length}</span>
                  </div>
                </div>

                {node.dependencies.length > 0 && (
                  <div className="mt-3">
                    <span className="font-medium text-blue-800">Depends on:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {node.dependencies.map(dep => (
                        <button
                          key={dep}
                          onClick={() => setSelectedNode(dep)}
                          className="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs rounded"
                        >
                          {nodes.find(n => n.stepId === dep)?.stepName || dep}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {node.dependents.length > 0 && (
                  <div className="mt-3">
                    <span className="font-medium text-blue-800">Required by:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {node.dependents.map(dep => (
                        <button
                          key={dep}
                          onClick={() => setSelectedNode(dep)}
                          className="px-2 py-1 bg-blue-100 hover:bg-blue-200 text-blue-800 text-xs rounded"
                        >
                          {nodes.find(n => n.stepId === dep)?.stepName || dep}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            );
          })()}
        </div>
      )}

      {/* Critical Path Info */}
      {showCriticalPath && criticalPath.length > 0 && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <h4 className="font-medium text-red-900 mb-2">Critical Path</h4>
          <div className="text-sm text-red-700">
            <span className="font-medium">Length:</span> {criticalPath.length} steps
          </div>
          <div className="flex flex-wrap gap-1 mt-2">
            {criticalPath.map((stepId, index) => (
              <React.Fragment key={stepId}>
                <button
                  onClick={() => setSelectedNode(stepId)}
                  className="px-2 py-1 bg-red-100 hover:bg-red-200 text-red-800 text-xs rounded"
                >
                  {nodes.find(n => n.stepId === stepId)?.stepName || stepId}
                </button>
                {index < criticalPath.length - 1 && (
                  <span className="text-red-600 text-xs self-center">→</span>
                )}
              </React.Fragment>
            ))}
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-green-500 rounded-full"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
            <span>Running</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
            <span>Failed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
            <span>Pending</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-1 bg-gray-400"></div>
            <span>Dependency</span>
          </div>
          {showCriticalPath && (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-1 bg-red-500"></div>
              <span>Critical Path</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default DependencyGraph;
