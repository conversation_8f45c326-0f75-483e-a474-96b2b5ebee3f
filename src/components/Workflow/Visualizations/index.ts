/**
 * Workflow Visualization Components
 * Export all visualization chart components
 */

export { default as Gantt<PERSON>hart } from './GanttChart';
export { default as DependencyGraph } from './DependencyGraph';
export { default as PerformanceChart } from './PerformanceChart';
export { default as CostAnalysisChart } from './CostAnalysisChart';

// Component prop interfaces
export interface GanttChartProps {
  steps: any[];
  startTime?: string;
  currentTime?: Date;
  showEstimates?: boolean;
  showProgress?: boolean;
  showCriticalPath?: boolean;
  timeScale?: 'minutes' | 'hours' | 'days';
  className?: string;
}

export interface DependencyGraphProps {
  steps: any[];
  dependencies: Record<string, string[]>;
  currentStep?: string;
  onStepClick?: (stepId: string) => void;
  layout?: 'hierarchical' | 'circular' | 'force';
  showLabels?: boolean;
  showCriticalPath?: boolean;
  className?: string;
}

export interface PerformanceChartProps {
  steps: any[];
  historicalData?: any[];
  chartType?: 'bar' | 'line' | 'scatter' | 'heatmap';
  metric?: 'duration' | 'efficiency' | 'throughput' | 'errorRate';
  showTrends?: boolean;
  showComparison?: boolean;
  timeRange?: '1h' | '24h' | '7d' | '30d';
  className?: string;
}

export interface CostAnalysisChartProps {
  costData: any[];
  budget?: any;
  chartType?: 'pie' | 'bar' | 'line' | 'treemap';
  groupBy?: 'step' | 'model' | 'provider' | 'time';
  showBudget?: boolean;
  showEfficiency?: boolean;
  timeRange?: '1h' | '24h' | '7d' | '30d';
  className?: string;
}
