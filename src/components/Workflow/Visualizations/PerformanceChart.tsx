/**
 * Performance Chart Component
 * Visualizes workflow performance metrics and trends
 */

'use client';

import React, { useState, useMemo } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface PerformanceMetrics {
  stepId: string;
  stepName?: string;
  duration: number;
  status: string;
  efficiency: number;
  throughput: number;
  errorRate: number;
  averageDuration: number;
  trend: 'up' | 'down' | 'stable';
}

interface PerformanceChartProps {
  steps: StepProgressInfo[];
  historicalData?: PerformanceMetrics[];
  chartType?: 'bar' | 'line' | 'scatter' | 'heatmap';
  metric?: 'duration' | 'efficiency' | 'throughput' | 'errorRate';
  showTrends?: boolean;
  showComparison?: boolean;
  timeRange?: '1h' | '24h' | '7d' | '30d';
  className?: string;
}

export function PerformanceChart({
  steps,
  historicalData = [],
  chartType = 'bar',
  metric = 'duration',
  showTrends = true,
  showComparison = false,
  timeRange = '24h',
  className = ''
}: PerformanceChartProps) {
  const [selectedMetric, setSelectedMetric] = useState(metric);
  const [selectedChartType, setSelectedChartType] = useState(chartType);
  const [hoveredStep, setHoveredStep] = useState<string | null>(null);

  // Calculate performance metrics
  const performanceData = useMemo(() => {
    const metrics: PerformanceMetrics[] = [];
    
    steps.forEach(step => {
      const historical = historicalData.filter(h => h.stepId === step.stepId);
      const avgDuration = historical.length > 0 
        ? historical.reduce((sum, h) => sum + h.duration, 0) / historical.length
        : step.duration || 0;
      
      const efficiency = step.duration && avgDuration 
        ? Math.max(0, (avgDuration - step.duration) / avgDuration * 100)
        : 0;
      
      const errorRate = historical.length > 0
        ? (historical.filter(h => h.status === 'failed').length / historical.length) * 100
        : step.status === 'failed' ? 100 : 0;

      metrics.push({
        stepId: step.stepId,
        stepName: step.stepName,
        duration: step.duration || 0,
        status: step.status,
        efficiency,
        throughput: step.duration ? 3600000 / step.duration : 0, // steps per hour
        errorRate,
        averageDuration: avgDuration,
        trend: step.duration && avgDuration 
          ? step.duration < avgDuration * 0.9 ? 'up' 
          : step.duration > avgDuration * 1.1 ? 'down' 
          : 'stable'
          : 'stable'
      });
    });

    return metrics.sort((a, b) => {
      switch (selectedMetric) {
        case 'duration': return b.duration - a.duration;
        case 'efficiency': return b.efficiency - a.efficiency;
        case 'throughput': return b.throughput - a.throughput;
        case 'errorRate': return b.errorRate - a.errorRate;
        default: return 0;
      }
    });
  }, [steps, historicalData, selectedMetric]);

  const getMetricValue = (data: PerformanceMetrics) => {
    switch (selectedMetric) {
      case 'duration': return data.duration / 1000; // Convert to seconds
      case 'efficiency': return data.efficiency;
      case 'throughput': return data.throughput;
      case 'errorRate': return data.errorRate;
      default: return 0;
    }
  };

  const getMetricLabel = () => {
    switch (selectedMetric) {
      case 'duration': return 'Duration (seconds)';
      case 'efficiency': return 'Efficiency (%)';
      case 'throughput': return 'Throughput (steps/hour)';
      case 'errorRate': return 'Error Rate (%)';
      default: return '';
    }
  };

  const getMetricColor = (value: number, data: PerformanceMetrics) => {
    switch (selectedMetric) {
      case 'duration':
        return value > data.averageDuration / 1000 ? 'bg-red-500' : 'bg-green-500';
      case 'efficiency':
        return value > 50 ? 'bg-green-500' : value > 0 ? 'bg-yellow-500' : 'bg-red-500';
      case 'throughput':
        return value > 10 ? 'bg-green-500' : value > 5 ? 'bg-yellow-500' : 'bg-red-500';
      case 'errorRate':
        return value > 10 ? 'bg-red-500' : value > 5 ? 'bg-yellow-500' : 'bg-green-500';
      default:
        return 'bg-blue-500';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      default: return '➡️';
    }
  };

  const maxValue = Math.max(...performanceData.map(getMetricValue));
  const minValue = Math.min(...performanceData.map(getMetricValue));

  // Bar Chart
  const BarChart = () => (
    <div className="space-y-3">
      {performanceData.map((data, index) => {
        const value = getMetricValue(data);
        const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
        const isHovered = hoveredStep === data.stepId;
        
        return (
          <div 
            key={data.stepId}
            className={`p-3 rounded-lg transition-all duration-200 ${
              isHovered ? 'bg-blue-50 shadow-md' : 'bg-gray-50'
            }`}
            onMouseEnter={() => setHoveredStep(data.stepId)}
            onMouseLeave={() => setHoveredStep(null)}
          >
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-900">
                  {data.stepName || data.stepId}
                </span>
                {showTrends && (
                  <span className="text-xs">{getTrendIcon(data.trend)}</span>
                )}
              </div>
              <div className="text-sm font-bold text-gray-900">
                {value.toFixed(selectedMetric === 'duration' ? 1 : 0)}
                {selectedMetric === 'efficiency' || selectedMetric === 'errorRate' ? '%' : ''}
              </div>
            </div>
            
            <div className="relative h-4 bg-gray-200 rounded-full overflow-hidden">
              <div 
                className={`h-full transition-all duration-500 ${getMetricColor(value, data)}`}
                style={{ width: `${percentage}%` }}
              />
            </div>
            
            {showComparison && data.averageDuration > 0 && selectedMetric === 'duration' && (
              <div className="text-xs text-gray-500 mt-1">
                Avg: {(data.averageDuration / 1000).toFixed(1)}s
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  // Line Chart (simplified)
  const LineChart = () => {
    const chartHeight = 200;
    const chartWidth = 400;
    const padding = 40;
    
    const points = performanceData.map((data, index) => {
      const x = (index / (performanceData.length - 1)) * (chartWidth - 2 * padding) + padding;
      const y = chartHeight - padding - ((getMetricValue(data) - minValue) / (maxValue - minValue)) * (chartHeight - 2 * padding);
      return { x, y, data };
    });

    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <svg width={chartWidth} height={chartHeight} className="w-full">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
            <line
              key={ratio}
              x1={padding}
              y1={padding + ratio * (chartHeight - 2 * padding)}
              x2={chartWidth - padding}
              y2={padding + ratio * (chartHeight - 2 * padding)}
              stroke="#E5E7EB"
              strokeWidth="1"
            />
          ))}
          
          {/* Line */}
          <polyline
            points={points.map(p => `${p.x},${p.y}`).join(' ')}
            fill="none"
            stroke="#3B82F6"
            strokeWidth="2"
          />
          
          {/* Points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill="#3B82F6"
              className="cursor-pointer hover:r-6 transition-all"
              onMouseEnter={() => setHoveredStep(point.data.stepId)}
              onMouseLeave={() => setHoveredStep(null)}
            />
          ))}
          
          {/* Labels */}
          <text x={padding} y={chartHeight - 10} className="text-xs fill-gray-600">
            {performanceData[0]?.stepName || 'Start'}
          </text>
          <text x={chartWidth - padding} y={chartHeight - 10} className="text-xs fill-gray-600" textAnchor="end">
            {performanceData[performanceData.length - 1]?.stepName || 'End'}
          </text>
        </svg>
      </div>
    );
  };

  // Scatter Plot
  const ScatterPlot = () => (
    <div className="bg-gray-50 rounded-lg p-4">
      <div className="grid grid-cols-4 gap-2">
        {performanceData.map((data, index) => {
          const size = Math.max(8, Math.min(32, (getMetricValue(data) / maxValue) * 32));
          return (
            <div
              key={data.stepId}
              className="flex flex-col items-center p-2 hover:bg-white rounded transition-colors"
              onMouseEnter={() => setHoveredStep(data.stepId)}
              onMouseLeave={() => setHoveredStep(null)}
            >
              <div 
                className={`rounded-full ${getMetricColor(getMetricValue(data), data)} opacity-75`}
                style={{ width: size, height: size }}
              />
              <div className="text-xs text-center mt-1 text-gray-600">
                {data.stepName || data.stepId}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Heatmap
  const Heatmap = () => {
    const cols = Math.ceil(Math.sqrt(performanceData.length));
    const rows = Math.ceil(performanceData.length / cols);
    
    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <div 
          className="grid gap-1"
          style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
        >
          {performanceData.map((data, index) => {
            const intensity = maxValue > 0 ? getMetricValue(data) / maxValue : 0;
            const opacity = Math.max(0.1, intensity);
            
            return (
              <div
                key={data.stepId}
                className={`aspect-square rounded flex items-center justify-center text-xs font-medium cursor-pointer transition-all hover:scale-110 ${
                  getMetricColor(getMetricValue(data), data)
                }`}
                style={{ opacity }}
                onMouseEnter={() => setHoveredStep(data.stepId)}
                onMouseLeave={() => setHoveredStep(null)}
                title={`${data.stepName || data.stepId}: ${getMetricValue(data).toFixed(1)}`}
              >
                {index + 1}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Performance Chart</h3>
          <p className="text-sm text-gray-600">
            Workflow step performance metrics
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={selectedMetric}
            onChange={(e) => setSelectedMetric(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="duration">Duration</option>
            <option value="efficiency">Efficiency</option>
            <option value="throughput">Throughput</option>
            <option value="errorRate">Error Rate</option>
          </select>
          
          <select
            value={selectedChartType}
            onChange={(e) => setSelectedChartType(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="bar">Bar Chart</option>
            <option value="line">Line Chart</option>
            <option value="scatter">Scatter Plot</option>
            <option value="heatmap">Heatmap</option>
          </select>
        </div>
      </div>

      {/* Chart */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium text-gray-900">{getMetricLabel()}</h4>
          {hoveredStep && (
            <div className="text-sm text-gray-600">
              Hovering: {performanceData.find(d => d.stepId === hoveredStep)?.stepName || hoveredStep}
            </div>
          )}
        </div>
        
        {selectedChartType === 'bar' && <BarChart />}
        {selectedChartType === 'line' && <LineChart />}
        {selectedChartType === 'scatter' && <ScatterPlot />}
        {selectedChartType === 'heatmap' && <Heatmap />}
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {performanceData.length}
          </div>
          <div className="text-sm text-gray-600">Total Steps</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {maxValue.toFixed(selectedMetric === 'duration' ? 1 : 0)}
          </div>
          <div className="text-sm text-gray-600">Max {selectedMetric}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">
            {(performanceData.reduce((sum, d) => sum + getMetricValue(d), 0) / performanceData.length).toFixed(selectedMetric === 'duration' ? 1 : 0)}
          </div>
          <div className="text-sm text-gray-600">Avg {selectedMetric}</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {performanceData.filter(d => d.trend === 'up').length}
          </div>
          <div className="text-sm text-gray-600">Improving</div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="pt-6 border-t border-gray-200">
        <h4 className="text-md font-medium text-gray-900 mb-3">Performance Insights</h4>
        <div className="space-y-2 text-sm">
          {performanceData.slice(0, 3).map((data, index) => (
            <div key={data.stepId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
              <span className="text-gray-700">
                {index === 0 ? '🏆' : index === 1 ? '🥈' : '🥉'} {data.stepName || data.stepId}
              </span>
              <span className="font-medium text-gray-900">
                {getMetricValue(data).toFixed(selectedMetric === 'duration' ? 1 : 0)}
                {selectedMetric === 'efficiency' || selectedMetric === 'errorRate' ? '%' : ''}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default PerformanceChart;
