/**
 * Cost Analysis Chart Component
 * Visualizes AI cost analysis and budget tracking
 */

'use client';

import React, { useState, useMemo } from 'react';

interface CostDataPoint {
  stepId: string;
  stepName?: string;
  cost: number;
  tokens: number;
  model: string;
  provider: string;
  timestamp: string;
  efficiency: number;
}

interface BudgetData {
  total: number;
  used: number;
  remaining: number;
  alertThreshold: number;
}

interface CostAnalysisChartProps {
  costData: CostDataPoint[];
  budget?: BudgetData;
  chartType?: 'pie' | 'bar' | 'line' | 'treemap';
  groupBy?: 'step' | 'model' | 'provider' | 'time';
  showBudget?: boolean;
  showEfficiency?: boolean;
  timeRange?: '1h' | '24h' | '7d' | '30d';
  className?: string;
}

export function CostAnalysisChart({
  costData,
  budget,
  chartType = 'pie',
  groupBy = 'step',
  showBudget = true,
  showEfficiency = false,
  timeRange = '24h',
  className = ''
}: CostAnalysisChartProps) {
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null);
  const [hoveredSegment, setHoveredSegment] = useState<string | null>(null);

  // Process and group cost data
  const processedData = useMemo(() => {
    const grouped: Record<string, { cost: number; tokens: number; count: number; efficiency: number }> = {};
    
    costData.forEach(item => {
      let key = '';
      switch (groupBy) {
        case 'step': key = item.stepName || item.stepId; break;
        case 'model': key = item.model; break;
        case 'provider': key = item.provider; break;
        case 'time': 
          const date = new Date(item.timestamp);
          key = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
          break;
        default: key = item.stepId;
      }
      
      if (!grouped[key]) {
        grouped[key] = { cost: 0, tokens: 0, count: 0, efficiency: 0 };
      }
      
      grouped[key].cost += item.cost;
      grouped[key].tokens += item.tokens;
      grouped[key].count += 1;
      grouped[key].efficiency += item.efficiency;
    });

    // Calculate average efficiency
    Object.values(grouped).forEach(group => {
      group.efficiency = group.efficiency / group.count;
    });

    return Object.entries(grouped)
      .map(([key, data]) => ({ key, ...data }))
      .sort((a, b) => b.cost - a.cost);
  }, [costData, groupBy]);

  const totalCost = processedData.reduce((sum, item) => sum + item.cost, 0);
  const totalTokens = processedData.reduce((sum, item) => sum + item.tokens, 0);

  const formatCost = (cost: number) => {
    if (cost < 0.01) return `$${(cost * 100).toFixed(2)}¢`;
    return `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`;
    return tokens.toString();
  };

  const getColor = (index: number) => {
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
      '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
    ];
    return colors[index % colors.length];
  };

  // Pie Chart
  const PieChart = () => {
    const radius = 80;
    const centerX = 100;
    const centerY = 100;
    
    let currentAngle = 0;
    const segments = processedData.map((item, index) => {
      const percentage = item.cost / totalCost;
      const angle = percentage * 2 * Math.PI;
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;
      
      const x1 = centerX + radius * Math.cos(startAngle);
      const y1 = centerY + radius * Math.sin(startAngle);
      const x2 = centerX + radius * Math.cos(endAngle);
      const y2 = centerY + radius * Math.sin(endAngle);
      
      const largeArcFlag = angle > Math.PI ? 1 : 0;
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      currentAngle += angle;
      
      return {
        ...item,
        pathData,
        percentage: percentage * 100,
        color: getColor(index),
        index
      };
    });

    return (
      <div className="flex items-center space-x-6">
        <svg width="200" height="200" className="flex-shrink-0">
          {segments.map(segment => (
            <path
              key={segment.key}
              d={segment.pathData}
              fill={segment.color}
              className={`cursor-pointer transition-all duration-200 ${
                hoveredSegment === segment.key ? 'opacity-80 transform scale-105' : ''
              } ${selectedSegment === segment.key ? 'stroke-2 stroke-gray-800' : ''}`}
              onMouseEnter={() => setHoveredSegment(segment.key)}
              onMouseLeave={() => setHoveredSegment(null)}
              onClick={() => setSelectedSegment(selectedSegment === segment.key ? null : segment.key)}
            />
          ))}
        </svg>
        
        <div className="flex-1 space-y-2">
          {segments.slice(0, 5).map(segment => (
            <div 
              key={segment.key}
              className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                hoveredSegment === segment.key ? 'bg-gray-100' : ''
              }`}
              onMouseEnter={() => setHoveredSegment(segment.key)}
              onMouseLeave={() => setHoveredSegment(null)}
              onClick={() => setSelectedSegment(selectedSegment === segment.key ? null : segment.key)}
            >
              <div className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: segment.color }}
                />
                <span className="text-sm font-medium text-gray-900 truncate">
                  {segment.key}
                </span>
              </div>
              <div className="text-sm text-gray-600">
                {formatCost(segment.cost)} ({segment.percentage.toFixed(1)}%)
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Bar Chart
  const BarChart = () => {
    const maxCost = Math.max(...processedData.map(item => item.cost));
    
    return (
      <div className="space-y-3">
        {processedData.slice(0, 10).map((item, index) => {
          const percentage = maxCost > 0 ? (item.cost / maxCost) * 100 : 0;
          const isHovered = hoveredSegment === item.key;
          
          return (
            <div 
              key={item.key}
              className={`p-3 rounded-lg transition-all duration-200 cursor-pointer ${
                isHovered ? 'bg-blue-50 shadow-md' : 'bg-gray-50'
              }`}
              onMouseEnter={() => setHoveredSegment(item.key)}
              onMouseLeave={() => setHoveredSegment(null)}
              onClick={() => setSelectedSegment(selectedSegment === item.key ? null : item.key)}
            >
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-900 truncate">
                  {item.key}
                </span>
                <div className="text-sm font-bold text-gray-900">
                  {formatCost(item.cost)}
                </div>
              </div>
              
              <div className="relative h-4 bg-gray-200 rounded-full overflow-hidden">
                <div 
                  className="h-full transition-all duration-500"
                  style={{ 
                    width: `${percentage}%`,
                    backgroundColor: getColor(index)
                  }}
                />
              </div>
              
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>{formatTokens(item.tokens)} tokens</span>
                {showEfficiency && (
                  <span>Efficiency: {item.efficiency.toFixed(1)}%</span>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  // Line Chart
  const LineChart = () => {
    if (groupBy !== 'time') {
      return (
        <div className="text-center text-gray-500 py-8">
          Line chart is only available when grouping by time
        </div>
      );
    }

    const chartHeight = 200;
    const chartWidth = 400;
    const padding = 40;
    
    const maxCost = Math.max(...processedData.map(item => item.cost));
    const minCost = Math.min(...processedData.map(item => item.cost));
    
    const points = processedData.map((item, index) => {
      const x = (index / (processedData.length - 1)) * (chartWidth - 2 * padding) + padding;
      const y = chartHeight - padding - ((item.cost - minCost) / (maxCost - minCost)) * (chartHeight - 2 * padding);
      return { x, y, item };
    });

    return (
      <div className="bg-gray-50 rounded-lg p-4">
        <svg width={chartWidth} height={chartHeight} className="w-full">
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map(ratio => (
            <line
              key={ratio}
              x1={padding}
              y1={padding + ratio * (chartHeight - 2 * padding)}
              x2={chartWidth - padding}
              y2={padding + ratio * (chartHeight - 2 * padding)}
              stroke="#E5E7EB"
              strokeWidth="1"
            />
          ))}
          
          {/* Line */}
          <polyline
            points={points.map(p => `${p.x},${p.y}`).join(' ')}
            fill="none"
            stroke="#3B82F6"
            strokeWidth="2"
          />
          
          {/* Points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill="#3B82F6"
              className="cursor-pointer hover:r-6 transition-all"
              onMouseEnter={() => setHoveredSegment(point.item.key)}
              onMouseLeave={() => setHoveredSegment(null)}
            />
          ))}
        </svg>
      </div>
    );
  };

  // Treemap (simplified grid)
  const Treemap = () => {
    const cols = Math.ceil(Math.sqrt(processedData.length));
    
    return (
      <div 
        className="grid gap-1 bg-gray-50 rounded-lg p-4"
        style={{ gridTemplateColumns: `repeat(${cols}, 1fr)` }}
      >
        {processedData.map((item, index) => {
          const size = Math.max(0.1, item.cost / totalCost);
          const opacity = Math.max(0.3, size);
          
          return (
            <div
              key={item.key}
              className="aspect-square rounded flex flex-col items-center justify-center text-xs font-medium cursor-pointer transition-all hover:scale-105"
              style={{ 
                backgroundColor: getColor(index),
                opacity
              }}
              onMouseEnter={() => setHoveredSegment(item.key)}
              onMouseLeave={() => setHoveredSegment(null)}
              onClick={() => setSelectedSegment(selectedSegment === item.key ? null : item.key)}
              title={`${item.key}: ${formatCost(item.cost)}`}
            >
              <div className="text-white text-center p-1">
                <div className="truncate">{item.key}</div>
                <div className="text-xs">{formatCost(item.cost)}</div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Cost Analysis</h3>
          <p className="text-sm text-gray-600">
            AI usage costs and budget tracking
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={groupBy}
            onChange={(e) => setGroupBy(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="step">By Step</option>
            <option value="model">By Model</option>
            <option value="provider">By Provider</option>
            <option value="time">By Time</option>
          </select>
          
          <select
            value={chartType}
            onChange={(e) => setChartType(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="pie">Pie Chart</option>
            <option value="bar">Bar Chart</option>
            <option value="line">Line Chart</option>
            <option value="treemap">Treemap</option>
          </select>
        </div>
      </div>

      {/* Budget Alert */}
      {showBudget && budget && budget.used / budget.total > budget.alertThreshold && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="font-medium text-yellow-800">⚠️ Budget Alert</div>
          <div className="text-sm text-yellow-700 mt-1">
            You've used {((budget.used / budget.total) * 100).toFixed(1)}% of your budget
          </div>
        </div>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {formatCost(totalCost)}
          </div>
          <div className="text-sm text-gray-600">Total Cost</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            {formatTokens(totalTokens)}
          </div>
          <div className="text-sm text-gray-600">Total Tokens</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {processedData.length}
          </div>
          <div className="text-sm text-gray-600">Items</div>
        </div>
        {budget && (
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {formatCost(budget.remaining)}
            </div>
            <div className="text-sm text-gray-600">Remaining</div>
          </div>
        )}
      </div>

      {/* Budget Progress */}
      {showBudget && budget && (
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Budget Usage</span>
            <span>{((budget.used / budget.total) * 100).toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className={`h-3 rounded-full transition-all duration-500 ${
                budget.used / budget.total > 0.9 ? 'bg-red-500' :
                budget.used / budget.total > budget.alertThreshold ? 'bg-yellow-500' :
                'bg-green-500'
              }`}
              style={{ width: `${Math.min((budget.used / budget.total) * 100, 100)}%` }}
            />
          </div>
        </div>
      )}

      {/* Chart */}
      <div className="mb-6">
        {chartType === 'pie' && <PieChart />}
        {chartType === 'bar' && <BarChart />}
        {chartType === 'line' && <LineChart />}
        {chartType === 'treemap' && <Treemap />}
      </div>

      {/* Selected Item Details */}
      {selectedSegment && (
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          {(() => {
            const item = processedData.find(d => d.key === selectedSegment);
            if (!item) return null;

            return (
              <div>
                <h4 className="font-medium text-blue-900 mb-2">{item.key}</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Cost:</span>
                    <span className="ml-2 text-blue-700">{formatCost(item.cost)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Tokens:</span>
                    <span className="ml-2 text-blue-700">{formatTokens(item.tokens)}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Requests:</span>
                    <span className="ml-2 text-blue-700">{item.count}</span>
                  </div>
                  <div>
                    <span className="font-medium text-blue-800">Efficiency:</span>
                    <span className="ml-2 text-blue-700">{item.efficiency.toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}

export default CostAnalysisChart;
