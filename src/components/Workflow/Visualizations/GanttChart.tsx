/**
 * Gantt Chart Component
 * Timeline visualization for workflow step timing and scheduling
 */

'use client';

import React, { useState, useMemo } from 'react';
import { StepProgressInfo } from '../../../hooks/useWorkflowProgress';

interface GanttStep extends StepProgressInfo {
  estimatedDuration?: number;
  actualStart?: Date;
  actualEnd?: Date;
  estimatedStart?: Date;
  estimatedEnd?: Date;
  progress?: number;
}

interface GanttChartProps {
  steps: GanttStep[];
  startTime?: string;
  currentTime?: Date;
  showEstimates?: boolean;
  showProgress?: boolean;
  showCriticalPath?: boolean;
  timeScale?: 'minutes' | 'hours' | 'days';
  className?: string;
}

export function GanttChart({
  steps,
  startTime,
  currentTime = new Date(),
  showEstimates = true,
  showProgress = true,
  showCriticalPath = false,
  timeScale = 'minutes',
  className = ''
}: GanttChartProps) {
  const [selectedStep, setSelectedStep] = useState<string | null>(null);
  const [viewRange, setViewRange] = useState<'all' | 'current' | 'remaining'>('all');

  // Calculate time bounds and scale
  const timeBounds = useMemo(() => {
    const start = startTime ? new Date(startTime) : new Date();
    const allTimes = steps.flatMap(step => [
      step.actualStart || (step.startedAt ? new Date(step.startedAt) : null),
      step.actualEnd || (step.completedAt ? new Date(step.completedAt) : null),
      step.estimatedStart,
      step.estimatedEnd
    ]).filter(Boolean) as Date[];

    if (allTimes.length === 0) {
      return {
        start,
        end: new Date(start.getTime() + 3600000), // 1 hour default
        duration: 3600000
      };
    }

    const minTime = new Date(Math.min(start.getTime(), ...allTimes.map(t => t.getTime())));
    const maxTime = new Date(Math.max(currentTime.getTime(), ...allTimes.map(t => t.getTime())));
    
    return {
      start: minTime,
      end: maxTime,
      duration: maxTime.getTime() - minTime.getTime()
    };
  }, [steps, startTime, currentTime]);

  const getTimeUnit = () => {
    switch (timeScale) {
      case 'hours': return 3600000;
      case 'days': return 86400000;
      default: return 60000; // minutes
    }
  };

  const formatTime = (date: Date) => {
    switch (timeScale) {
      case 'hours':
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      case 'days':
        return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
      default:
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const getPositionX = (date: Date) => {
    const elapsed = date.getTime() - timeBounds.start.getTime();
    return (elapsed / timeBounds.duration) * 100;
  };

  const getWidth = (startDate: Date, endDate: Date) => {
    const duration = endDate.getTime() - startDate.getTime();
    return (duration / timeBounds.duration) * 100;
  };

  const getStatusColor = (status: string, isEstimate = false) => {
    const opacity = isEstimate ? '60' : '';
    switch (status) {
      case 'completed': return `bg-green-500${opacity}`;
      case 'running': return `bg-blue-500${opacity}`;
      case 'failed': return `bg-red-500${opacity}`;
      case 'waiting_review': return `bg-purple-500${opacity}`;
      case 'waiting_approval': return `bg-orange-500${opacity}`;
      case 'pending': return `bg-gray-300${opacity}`;
      default: return `bg-gray-300${opacity}`;
    }
  };

  const processedSteps = useMemo(() => {
    return steps.map((step, index) => {
      const actualStart = step.actualStart || (step.startedAt ? new Date(step.startedAt) : null);
      const actualEnd = step.actualEnd || (step.completedAt ? new Date(step.completedAt) : null);
      
      // Calculate estimated times if not provided
      let estimatedStart = step.estimatedStart;
      let estimatedEnd = step.estimatedEnd;
      
      if (!estimatedStart && startTime) {
        const baseStart = new Date(startTime);
        estimatedStart = new Date(baseStart.getTime() + (index * (step.estimatedDuration || 300000)));
      }
      
      if (!estimatedEnd && estimatedStart) {
        estimatedEnd = new Date(estimatedStart.getTime() + (step.estimatedDuration || 300000));
      }

      return {
        ...step,
        actualStart,
        actualEnd,
        estimatedStart,
        estimatedEnd,
        index
      };
    });
  }, [steps, startTime]);

  const filteredSteps = useMemo(() => {
    switch (viewRange) {
      case 'current':
        return processedSteps.filter(step => 
          step.status === 'running' || step.status === 'waiting_review' || step.status === 'waiting_approval'
        );
      case 'remaining':
        return processedSteps.filter(step => 
          step.status !== 'completed' && step.status !== 'failed'
        );
      default:
        return processedSteps;
    }
  }, [processedSteps, viewRange]);

  // Generate time markers
  const timeMarkers = useMemo(() => {
    const markers = [];
    const unit = getTimeUnit();
    const start = new Date(Math.floor(timeBounds.start.getTime() / unit) * unit);
    
    for (let time = start.getTime(); time <= timeBounds.end.getTime(); time += unit) {
      const date = new Date(time);
      markers.push({
        date,
        position: getPositionX(date),
        label: formatTime(date)
      });
    }
    
    return markers;
  }, [timeBounds, timeScale]);

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Gantt Chart</h3>
          <p className="text-sm text-gray-600">
            Timeline view of workflow execution
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={viewRange}
            onChange={(e) => setViewRange(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Steps</option>
            <option value="current">Current</option>
            <option value="remaining">Remaining</option>
          </select>
          
          <select
            value={timeScale}
            onChange={(e) => setTimeScale(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="minutes">Minutes</option>
            <option value="hours">Hours</option>
            <option value="days">Days</option>
          </select>
        </div>
      </div>

      {/* Chart Container */}
      <div className="overflow-x-auto">
        <div className="min-w-full" style={{ minWidth: '800px' }}>
          {/* Time Scale */}
          <div className="relative h-8 mb-4 border-b border-gray-200">
            {timeMarkers.map((marker, index) => (
              <div
                key={index}
                className="absolute top-0 h-full flex flex-col items-center"
                style={{ left: `${marker.position}%` }}
              >
                <div className="w-px h-4 bg-gray-300" />
                <div className="text-xs text-gray-500 mt-1">
                  {marker.label}
                </div>
              </div>
            ))}
            
            {/* Current Time Indicator */}
            <div
              className="absolute top-0 h-full w-px bg-red-500 z-10"
              style={{ left: `${getPositionX(currentTime)}%` }}
            >
              <div className="absolute -top-2 -left-1 w-2 h-2 bg-red-500 rounded-full" />
              <div className="absolute top-6 -left-8 text-xs text-red-600 font-medium">
                Now
              </div>
            </div>
          </div>

          {/* Steps */}
          <div className="space-y-3">
            {filteredSteps.map((step) => (
              <div key={step.stepId} className="relative">
                {/* Step Label */}
                <div className="flex items-center mb-2">
                  <div className="w-48 text-sm font-medium text-gray-900 truncate">
                    {step.stepName || step.stepId}
                  </div>
                  <div className="text-xs text-gray-500 ml-2 capitalize">
                    {step.status.replace('_', ' ')}
                  </div>
                </div>

                {/* Timeline Bar */}
                <div className="relative h-8 bg-gray-100 rounded">
                  {/* Estimated Bar */}
                  {showEstimates && step.estimatedStart && step.estimatedEnd && (
                    <div
                      className={`absolute top-1 h-6 rounded border-2 border-dashed border-gray-400 ${getStatusColor(step.status, true)}`}
                      style={{
                        left: `${getPositionX(step.estimatedStart)}%`,
                        width: `${getWidth(step.estimatedStart, step.estimatedEnd)}%`
                      }}
                      title={`Estimated: ${formatTime(step.estimatedStart)} - ${formatTime(step.estimatedEnd)}`}
                    />
                  )}

                  {/* Actual Bar */}
                  {step.actualStart && (
                    <div
                      className={`absolute top-1 h-6 rounded ${getStatusColor(step.status)} cursor-pointer transition-all duration-200 hover:shadow-md ${
                        selectedStep === step.stepId ? 'ring-2 ring-blue-300' : ''
                      }`}
                      style={{
                        left: `${getPositionX(step.actualStart)}%`,
                        width: step.actualEnd 
                          ? `${getWidth(step.actualStart, step.actualEnd)}%`
                          : `${getWidth(step.actualStart, currentTime)}%`
                      }}
                      onClick={() => setSelectedStep(selectedStep === step.stepId ? null : step.stepId)}
                      title={`Actual: ${formatTime(step.actualStart)} - ${step.actualEnd ? formatTime(step.actualEnd) : 'ongoing'}`}
                    >
                      {/* Progress Bar */}
                      {showProgress && step.progress !== undefined && step.status === 'running' && (
                        <div
                          className="h-full bg-white bg-opacity-30 rounded"
                          style={{ width: `${step.progress}%` }}
                        />
                      )}
                    </div>
                  )}

                  {/* Step Status Indicator */}
                  <div className="absolute right-2 top-1 bottom-1 flex items-center">
                    <div className={`w-4 h-4 rounded-full ${getStatusColor(step.status)} flex items-center justify-center text-xs text-white font-bold`}>
                      {step.status === 'completed' ? '✓' : 
                       step.status === 'failed' ? '✗' : 
                       step.status === 'running' ? '⟳' : '○'}
                    </div>
                  </div>
                </div>

                {/* Duration Info */}
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>
                    {step.actualStart ? formatTime(step.actualStart) : 'Not started'}
                  </span>
                  <span>
                    {step.duration ? `${Math.round(step.duration / 60000)}m` : ''}
                  </span>
                  <span>
                    {step.actualEnd ? formatTime(step.actualEnd) : step.status === 'running' ? 'In progress' : ''}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Selected Step Details */}
      {selectedStep && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          {(() => {
            const step = processedSteps.find(s => s.stepId === selectedStep);
            if (!step) return null;

            return (
              <div>
                <h4 className="font-medium text-blue-900 mb-2">
                  {step.stepName || step.stepId}
                </h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-blue-800">Status:</span>
                    <span className="ml-2 text-blue-700 capitalize">
                      {step.status.replace('_', ' ')}
                    </span>
                  </div>
                  {step.duration && (
                    <div>
                      <span className="font-medium text-blue-800">Duration:</span>
                      <span className="ml-2 text-blue-700">
                        {Math.round(step.duration / 60000)}m {Math.round((step.duration % 60000) / 1000)}s
                      </span>
                    </div>
                  )}
                  {step.actualStart && (
                    <div>
                      <span className="font-medium text-blue-800">Started:</span>
                      <span className="ml-2 text-blue-700">
                        {step.actualStart.toLocaleString()}
                      </span>
                    </div>
                  )}
                  {step.actualEnd && (
                    <div>
                      <span className="font-medium text-blue-800">Completed:</span>
                      <span className="ml-2 text-blue-700">
                        {step.actualEnd.toLocaleString()}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            );
          })()}
        </div>
      )}

      {/* Legend */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-2 bg-green-500 rounded"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-2 bg-blue-500 rounded"></div>
            <span>Running</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-2 bg-red-500 rounded"></div>
            <span>Failed</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-2 bg-gray-300 rounded"></div>
            <span>Pending</span>
          </div>
          {showEstimates && (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-2 bg-gray-300 border-2 border-dashed border-gray-400 rounded"></div>
              <span>Estimated</span>
            </div>
          )}
          <div className="flex items-center space-x-2">
            <div className="w-px h-4 bg-red-500"></div>
            <span>Current Time</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default GanttChart;
