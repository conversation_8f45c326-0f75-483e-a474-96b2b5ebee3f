/**
 * Execution List Component
 * Displays a list of workflow executions with filtering and sorting
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface ExecutionItem {
  id: string;
  templateId: string;
  templateName: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  progress: number;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  cost?: number;
  tokens?: number;
  userId?: string;
  error?: string;
  tags?: string[];
}

interface ExecutionListProps {
  executions: ExecutionItem[];
  onExecutionClick?: (execution: ExecutionItem) => void;
  onExecutionDelete?: (executionId: string) => void;
  showActions?: boolean;
  showCosts?: boolean;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export function ExecutionList({
  executions,
  onExecutionClick,
  onExecutionDelete,
  showActions = true,
  showCosts = true,
  showProgress = true,
  compact = false,
  className = ''
}: ExecutionListProps) {
  const [selectedExecutions, setSelectedExecutions] = useState<Set<string>>(new Set());
  const [sortBy, setSortBy] = useState<'startedAt' | 'duration' | 'cost' | 'status'>('startedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');

  const formatDuration = (ms?: number) => {
    if (!ms) return 'N/A';
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatCost = (cost?: number) => {
    if (!cost) return 'N/A';
    return cost < 0.01 ? `$${(cost * 100).toFixed(2)}¢` : `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens?: number) => {
    if (!tokens) return 'N/A';
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`;
    return tokens.toString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'failed': return 'text-red-600 bg-red-50 border-red-200';
      case 'running': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'paused': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'running': return '🔄';
      case 'paused': return '⏸️';
      default: return '⚪';
    }
  };

  const sortedExecutions = [...executions].sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy) {
      case 'startedAt':
        aValue = new Date(a.startedAt).getTime();
        bValue = new Date(b.startedAt).getTime();
        break;
      case 'duration':
        aValue = a.duration || 0;
        bValue = b.duration || 0;
        break;
      case 'cost':
        aValue = a.cost || 0;
        bValue = b.cost || 0;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      default:
        return 0;
    }
    
    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
  });

  const toggleSelection = (executionId: string) => {
    const newSelected = new Set(selectedExecutions);
    if (newSelected.has(executionId)) {
      newSelected.delete(executionId);
    } else {
      newSelected.add(executionId);
    }
    setSelectedExecutions(newSelected);
  };

  const selectAll = () => {
    if (selectedExecutions.size === executions.length) {
      setSelectedExecutions(new Set());
    } else {
      setSelectedExecutions(new Set(executions.map(e => e.id)));
    }
  };

  const handleBulkDelete = () => {
    if (onExecutionDelete && selectedExecutions.size > 0) {
      selectedExecutions.forEach(id => onExecutionDelete(id));
      setSelectedExecutions(new Set());
    }
  };

  // List View
  const ListView = () => (
    <div className="space-y-2">
      {sortedExecutions.map((execution) => (
        <div
          key={execution.id}
          className={`p-4 border rounded-lg transition-all duration-200 hover:shadow-md cursor-pointer ${
            selectedExecutions.has(execution.id) ? 'ring-2 ring-blue-300 bg-blue-50' : 'bg-white'
          }`}
          onClick={() => onExecutionClick?.(execution)}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {showActions && (
                <input
                  type="checkbox"
                  checked={selectedExecutions.has(execution.id)}
                  onChange={(e) => {
                    e.stopPropagation();
                    toggleSelection(execution.id);
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              )}
              
              <div className="text-2xl">{getStatusIcon(execution.status)}</div>
              
              <div className="min-w-0 flex-1">
                <div className="font-medium text-gray-900 truncate">
                  {execution.templateName}
                </div>
                <div className="text-sm text-gray-600">
                  {execution.id.slice(0, 8)}... • Started {new Date(execution.startedAt).toLocaleString()}
                </div>
                {execution.tags && execution.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-1">
                    {execution.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                        {tag}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(execution.status)}`}>
                  {execution.status.toUpperCase()}
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">
                  {execution.completedSteps}/{execution.totalSteps}
                </div>
                <div className="text-xs text-gray-600">Steps</div>
              </div>
              
              <div className="text-center">
                <div className="text-sm font-medium text-gray-900">
                  {formatDuration(execution.duration)}
                </div>
                <div className="text-xs text-gray-600">Duration</div>
              </div>
              
              {showCosts && (
                <>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCost(execution.cost)}
                    </div>
                    <div className="text-xs text-gray-600">Cost</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {formatTokens(execution.tokens)}
                    </div>
                    <div className="text-xs text-gray-600">Tokens</div>
                  </div>
                </>
              )}
              
              {showActions && (
                <div className="flex items-center space-x-2">
                  <Link
                    href={`/history/${execution.id}`}
                    className="p-1 text-blue-600 hover:text-blue-800"
                    onClick={(e) => e.stopPropagation()}
                  >
                    👁️
                  </Link>
                  {onExecutionDelete && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onExecutionDelete(execution.id);
                      }}
                      className="p-1 text-red-600 hover:text-red-800"
                    >
                      🗑️
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Progress Bar */}
          {showProgress && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{execution.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    execution.status === 'completed' ? 'bg-green-500' :
                    execution.status === 'failed' ? 'bg-red-500' :
                    execution.status === 'running' ? 'bg-blue-500' :
                    'bg-yellow-500'
                  }`}
                  style={{ width: `${execution.progress}%` }}
                />
              </div>
            </div>
          )}
          
          {/* Error Message */}
          {execution.error && (
            <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              {execution.error}
            </div>
          )}
        </div>
      ))}
    </div>
  );

  // Grid View
  const GridView = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {sortedExecutions.map((execution) => (
        <div
          key={execution.id}
          className={`p-4 border rounded-lg transition-all duration-200 hover:shadow-md cursor-pointer ${
            selectedExecutions.has(execution.id) ? 'ring-2 ring-blue-300 bg-blue-50' : 'bg-white'
          }`}
          onClick={() => onExecutionClick?.(execution)}
        >
          <div className="flex items-center justify-between mb-3">
            <div className="text-2xl">{getStatusIcon(execution.status)}</div>
            {showActions && (
              <input
                type="checkbox"
                checked={selectedExecutions.has(execution.id)}
                onChange={(e) => {
                  e.stopPropagation();
                  toggleSelection(execution.id);
                }}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            )}
          </div>
          
          <div className="mb-3">
            <div className="font-medium text-gray-900 truncate">
              {execution.templateName}
            </div>
            <div className="text-sm text-gray-600">
              {execution.id.slice(0, 8)}...
            </div>
          </div>
          
          <div className={`px-2 py-1 rounded text-xs font-medium text-center mb-3 border ${getStatusColor(execution.status)}`}>
            {execution.status.toUpperCase()}
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Progress:</span>
              <span className="font-medium">{execution.progress}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Steps:</span>
              <span className="font-medium">{execution.completedSteps}/{execution.totalSteps}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Duration:</span>
              <span className="font-medium">{formatDuration(execution.duration)}</span>
            </div>
            {showCosts && (
              <>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cost:</span>
                  <span className="font-medium">{formatCost(execution.cost)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Tokens:</span>
                  <span className="font-medium">{formatTokens(execution.tokens)}</span>
                </div>
              </>
            )}
          </div>
          
          {showProgress && (
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    execution.status === 'completed' ? 'bg-green-500' :
                    execution.status === 'failed' ? 'bg-red-500' :
                    execution.status === 'running' ? 'bg-blue-500' :
                    'bg-yellow-500'
                  }`}
                  style={{ width: `${execution.progress}%` }}
                />
              </div>
            </div>
          )}
          
          <div className="text-xs text-gray-500 mt-3">
            {new Date(execution.startedAt).toLocaleString()}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className={className}>
      {/* Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          {showActions && (
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedExecutions.size === executions.length && executions.length > 0}
                onChange={selectAll}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-600">
                {selectedExecutions.size} selected
              </span>
              {selectedExecutions.size > 0 && onExecutionDelete && (
                <button
                  onClick={handleBulkDelete}
                  className="px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-700 rounded"
                >
                  Delete Selected
                </button>
              )}
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="startedAt">Sort by Start Time</option>
            <option value="duration">Sort by Duration</option>
            <option value="cost">Sort by Cost</option>
            <option value="status">Sort by Status</option>
          </select>
          
          <button
            onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
            className="p-1 text-gray-600 hover:text-gray-800"
            title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
          >
            {sortOrder === 'asc' ? '↑' : '↓'}
          </button>
          
          <div className="flex border border-gray-300 rounded">
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              List
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'grid' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Grid
            </button>
          </div>
        </div>
      </div>
      
      {/* Content */}
      {viewMode === 'list' ? <ListView /> : <GridView />}
      
      {/* Empty State */}
      {executions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No executions found
        </div>
      )}
    </div>
  );
}

export default ExecutionList;
