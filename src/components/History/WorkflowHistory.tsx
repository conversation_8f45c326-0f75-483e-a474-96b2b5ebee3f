/**
 * Workflow History Component
 * Main interface for viewing workflow execution history
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface WorkflowExecution {
  id: string;
  templateId: string;
  templateName: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  progress: number;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  cost?: number;
  tokens?: number;
  userId?: string;
  error?: string;
}

interface HistoryFilters {
  status?: string;
  templateId?: string;
  dateRange?: string;
  userId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface WorkflowHistoryProps {
  limit?: number;
  showFilters?: boolean;
  showStats?: boolean;
  onExecutionSelect?: (execution: WorkflowExecution) => void;
  className?: string;
}

export function WorkflowHistory({
  limit = 50,
  showFilters = true,
  showStats = true,
  onExecutionSelect,
  className = ''
}: WorkflowHistoryProps) {
  const [executions, setExecutions] = useState<WorkflowExecution[]>([]);
  const [filteredExecutions, setFilteredExecutions] = useState<WorkflowExecution[]>([]);
  const [filters, setFilters] = useState<HistoryFilters>({
    sortBy: 'startedAt',
    sortOrder: 'desc'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    offset: 0,
    hasMore: false,
    total: 0
  });

  // Fetch workflow history
  const fetchHistory = async (offset = 0) => {
    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.templateId && { templateId: filters.templateId }),
        ...(filters.userId && { userId: filters.userId }),
        ...(filters.sortBy && { sortBy: filters.sortBy }),
        ...(filters.sortOrder && { sortOrder: filters.sortOrder })
      });

      const response = await fetch(`/api/workflow/history?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch workflow history');
      }

      const data = await response.json();
      
      if (offset === 0) {
        setExecutions(data.data.executions);
      } else {
        setExecutions(prev => [...prev, ...data.data.executions]);
      }

      setPagination({
        offset: offset + data.data.executions.length,
        hasMore: data.data.pagination.hasMore,
        total: data.data.pagination.total
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  };

  // Apply filters
  useEffect(() => {
    let filtered = [...executions];

    // Apply date range filter
    if (filters.dateRange) {
      const now = new Date();
      let cutoffDate = new Date();
      
      switch (filters.dateRange) {
        case '24h':
          cutoffDate.setHours(now.getHours() - 24);
          break;
        case '7d':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case '30d':
          cutoffDate.setDate(now.getDate() - 30);
          break;
      }
      
      filtered = filtered.filter(exec => new Date(exec.startedAt) >= cutoffDate);
    }

    setFilteredExecutions(filtered);
  }, [executions, filters]);

  // Load initial data
  useEffect(() => {
    fetchHistory();
  }, [filters.status, filters.templateId, filters.userId, filters.sortBy, filters.sortOrder]);

  const formatDuration = (ms?: number) => {
    if (!ms) return '';
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
  };

  const formatCost = (cost?: number) => {
    if (!cost) return '';
    return cost < 0.01 ? `$${(cost * 100).toFixed(2)}¢` : `$${cost.toFixed(4)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50';
      case 'failed': return 'text-red-600 bg-red-50';
      case 'running': return 'text-blue-600 bg-blue-50';
      case 'paused': return 'text-yellow-600 bg-yellow-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅';
      case 'failed': return '❌';
      case 'running': return '🔄';
      case 'paused': return '⏸️';
      default: return '⚪';
    }
  };

  const calculateStats = () => {
    const total = filteredExecutions.length;
    const completed = filteredExecutions.filter(e => e.status === 'completed').length;
    const failed = filteredExecutions.filter(e => e.status === 'failed').length;
    const running = filteredExecutions.filter(e => e.status === 'running').length;
    const totalCost = filteredExecutions.reduce((sum, e) => sum + (e.cost || 0), 0);
    const avgDuration = completed > 0 
      ? filteredExecutions
          .filter(e => e.status === 'completed' && e.duration)
          .reduce((sum, e) => sum + (e.duration || 0), 0) / completed
      : 0;

    return {
      total,
      completed,
      failed,
      running,
      successRate: total > 0 ? (completed / total) * 100 : 0,
      totalCost,
      avgDuration
    };
  };

  const stats = calculateStats();

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Workflow History</h2>
          <p className="text-sm text-gray-600">
            {pagination.total} total executions
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => fetchHistory(0)}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-100 hover:bg-blue-200 text-blue-700 rounded disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Refresh'}
          </button>
          <Link
            href="/workflow/unified"
            className="px-3 py-1 text-sm bg-green-100 hover:bg-green-200 text-green-700 rounded"
          >
            New Workflow
          </Link>
        </div>
      </div>

      {/* Statistics */}
      {showStats && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <div className="text-sm text-gray-600">Total</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
            <div className="text-sm text-gray-600">Failed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
            <div className="text-sm text-gray-600">Running</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{Math.round(stats.successRate)}%</div>
            <div className="text-sm text-gray-600">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{formatCost(stats.totalCost)}</div>
            <div className="text-sm text-gray-600">Total Cost</div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className="flex flex-wrap gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <select
            value={filters.status || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value || undefined }))}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Statuses</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
            <option value="running">Running</option>
            <option value="paused">Paused</option>
          </select>

          <select
            value={filters.dateRange || ''}
            onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value || undefined }))}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Time</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>

          <select
            value={filters.sortBy || 'startedAt'}
            onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="startedAt">Start Time</option>
            <option value="completedAt">Completion Time</option>
            <option value="duration">Duration</option>
            <option value="cost">Cost</option>
            <option value="status">Status</option>
          </select>

          <select
            value={filters.sortOrder || 'desc'}
            onChange={(e) => setFilters(prev => ({ ...prev, sortOrder: e.target.value as 'asc' | 'desc' }))}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="desc">Newest First</option>
            <option value="asc">Oldest First</option>
          </select>

          <button
            onClick={() => setFilters({ sortBy: 'startedAt', sortOrder: 'desc' })}
            className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded"
          >
            Clear Filters
          </button>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="text-red-700">{error}</div>
        </div>
      )}

      {/* Execution List */}
      <div className="space-y-3">
        {filteredExecutions.map((execution) => (
          <div
            key={execution.id}
            className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onExecutionSelect?.(execution)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="text-2xl">{getStatusIcon(execution.status)}</div>
                <div>
                  <div className="font-medium text-gray-900">
                    {execution.templateName}
                  </div>
                  <div className="text-sm text-gray-600">
                    ID: {execution.id.slice(0, 8)}...
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(execution.status)}`}>
                    {execution.status.toUpperCase()}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {new Date(execution.startedAt).toLocaleString()}
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {execution.progress}%
                  </div>
                  <div className="text-xs text-gray-600">
                    {execution.completedSteps}/{execution.totalSteps} steps
                  </div>
                </div>
                
                {execution.duration && (
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {formatDuration(execution.duration)}
                    </div>
                    <div className="text-xs text-gray-600">Duration</div>
                  </div>
                )}
                
                {execution.cost && (
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCost(execution.cost)}
                    </div>
                    <div className="text-xs text-gray-600">Cost</div>
                  </div>
                )}
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    execution.status === 'completed' ? 'bg-green-500' :
                    execution.status === 'failed' ? 'bg-red-500' :
                    execution.status === 'running' ? 'bg-blue-500' :
                    'bg-yellow-500'
                  }`}
                  style={{ width: `${execution.progress}%` }}
                />
              </div>
            </div>

            {/* Error Message */}
            {execution.error && (
              <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                {execution.error}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Load More */}
      {pagination.hasMore && (
        <div className="mt-6 text-center">
          <button
            onClick={() => fetchHistory(pagination.offset)}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}

      {/* Empty State */}
      {filteredExecutions.length === 0 && !isLoading && (
        <div className="text-center py-8">
          <div className="text-gray-500 mb-4">No workflow executions found</div>
          <Link
            href="/workflow/unified"
            className="px-4 py-2 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded"
          >
            Start Your First Workflow
          </Link>
        </div>
      )}
    </div>
  );
}

export default WorkflowHistory;
