/**
 * Execution Details Component
 * Detailed view of a single workflow execution
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';

interface ExecutionDetails {
  id: string;
  templateId: string;
  templateName: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  progress: number;
  totalSteps: number;
  completedSteps: number;
  failedSteps: number;
  cost?: number;
  tokens?: number;
  userId?: string;
  error?: string;
  steps: ExecutionStep[];
  artifacts: ExecutionArtifact[];
  logs: ExecutionLog[];
  metadata?: Record<string, any>;
}

interface ExecutionStep {
  id: string;
  stepId: string;
  stepName: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  duration?: number;
  cost?: number;
  tokens?: number;
  input?: any;
  output?: any;
  error?: string;
  agentId?: string;
  agentName?: string;
}

interface ExecutionArtifact {
  id: string;
  stepId: string;
  type: string;
  name: string;
  content: any;
  size: number;
  createdAt: string;
  downloadUrl?: string;
}

interface ExecutionLog {
  id: string;
  timestamp: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  stepId?: string;
  agentId?: string;
  data?: any;
}

interface ExecutionDetailsProps {
  executionId: string;
  onClose?: () => void;
  className?: string;
}

export function ExecutionDetails({
  executionId,
  onClose,
  className = ''
}: ExecutionDetailsProps) {
  const [execution, setExecution] = useState<ExecutionDetails | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'steps' | 'artifacts' | 'logs'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch execution details
  useEffect(() => {
    const fetchExecution = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/workflow/executions/${executionId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch execution details');
        }

        const data = await response.json();
        setExecution(data.execution);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchExecution();
  }, [executionId]);

  const formatDuration = (ms?: number) => {
    if (!ms) return 'N/A';
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const formatCost = (cost?: number) => {
    if (!cost) return 'N/A';
    return cost < 0.01 ? `$${(cost * 100).toFixed(2)}¢` : `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens?: number) => {
    if (!tokens) return 'N/A';
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`;
    return tokens.toString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'failed': return 'text-red-600 bg-red-50 border-red-200';
      case 'running': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'paused': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'pending': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'skipped': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-600 bg-red-50';
      case 'warn': return 'text-yellow-600 bg-yellow-50';
      case 'info': return 'text-blue-600 bg-blue-50';
      case 'debug': return 'text-gray-600 bg-gray-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !execution) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center">
          <div className="text-red-600 mb-4">
            {error || 'Execution not found'}
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {execution.templateName}
            </h2>
            <p className="text-sm text-gray-600">
              Execution ID: {execution.id}
            </p>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(execution.status)}`}>
              {execution.status.toUpperCase()}
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview' },
            { id: 'steps', label: `Steps (${execution.steps.length})` },
            { id: 'artifacts', label: `Artifacts (${execution.artifacts.length})` },
            { id: 'logs', label: `Logs (${execution.logs.length})` }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Summary Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{execution.progress}%</div>
                <div className="text-sm text-gray-600">Progress</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {execution.completedSteps}/{execution.totalSteps}
                </div>
                <div className="text-sm text-gray-600">Steps</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatDuration(execution.duration)}
                </div>
                <div className="text-sm text-gray-600">Duration</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatCost(execution.cost)}
                </div>
                <div className="text-sm text-gray-600">Cost</div>
              </div>
            </div>

            {/* Progress Bar */}
            <div>
              <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                <span>Overall Progress</span>
                <span>{execution.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className={`h-3 rounded-full transition-all duration-300 ${
                    execution.status === 'completed' ? 'bg-green-500' :
                    execution.status === 'failed' ? 'bg-red-500' :
                    execution.status === 'running' ? 'bg-blue-500' :
                    'bg-yellow-500'
                  }`}
                  style={{ width: `${execution.progress}%` }}
                />
              </div>
            </div>

            {/* Timeline */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Timeline</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <span className="text-sm font-medium text-gray-900">Started</span>
                  <span className="text-sm text-gray-600">
                    {new Date(execution.startedAt).toLocaleString()}
                  </span>
                </div>
                {execution.completedAt && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span className="text-sm font-medium text-gray-900">Completed</span>
                    <span className="text-sm text-gray-600">
                      {new Date(execution.completedAt).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Error */}
            {execution.error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <h3 className="text-lg font-medium text-red-900 mb-2">Error</h3>
                <p className="text-sm text-red-700">{execution.error}</p>
              </div>
            )}

            {/* Metadata */}
            {execution.metadata && Object.keys(execution.metadata).length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {JSON.stringify(execution.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Steps Tab */}
        {activeTab === 'steps' && (
          <div className="space-y-4">
            {execution.steps.map((step, index) => (
              <div key={step.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="text-sm font-medium text-gray-500">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{step.stepName}</div>
                      <div className="text-sm text-gray-600">{step.stepId}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(step.status)}`}>
                      {step.status.toUpperCase()}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatDuration(step.duration)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatCost(step.cost)}
                    </div>
                  </div>
                </div>

                {step.error && (
                  <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    {step.error}
                  </div>
                )}

                {step.agentName && (
                  <div className="text-sm text-gray-600 mb-2">
                    Agent: {step.agentName}
                  </div>
                )}

                {(step.input || step.output) && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {step.input && (
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-2">Input</div>
                        <div className="bg-gray-50 rounded p-3 text-sm">
                          <pre className="whitespace-pre-wrap">
                            {typeof step.input === 'string' ? step.input : JSON.stringify(step.input, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                    {step.output && (
                      <div>
                        <div className="text-sm font-medium text-gray-700 mb-2">Output</div>
                        <div className="bg-gray-50 rounded p-3 text-sm">
                          <pre className="whitespace-pre-wrap">
                            {typeof step.output === 'string' ? step.output : JSON.stringify(step.output, null, 2)}
                          </pre>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Artifacts Tab */}
        {activeTab === 'artifacts' && (
          <div className="space-y-4">
            {execution.artifacts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No artifacts generated
              </div>
            ) : (
              execution.artifacts.map((artifact) => (
                <div key={artifact.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-gray-900">{artifact.name}</div>
                      <div className="text-sm text-gray-600">
                        {artifact.type} • {artifact.size} bytes • Step: {artifact.stepId}
                      </div>
                      <div className="text-xs text-gray-500">
                        Created: {new Date(artifact.createdAt).toLocaleString()}
                      </div>
                    </div>
                    
                    {artifact.downloadUrl && (
                      <a
                        href={artifact.downloadUrl}
                        download={artifact.name}
                        className="px-3 py-1 bg-blue-100 hover:bg-blue-200 text-blue-700 rounded text-sm"
                      >
                        Download
                      </a>
                    )}
                  </div>
                  
                  {typeof artifact.content === 'string' && artifact.content.length < 1000 && (
                    <div className="mt-3 bg-gray-50 rounded p-3">
                      <pre className="text-sm whitespace-pre-wrap">{artifact.content}</pre>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        )}

        {/* Logs Tab */}
        {activeTab === 'logs' && (
          <div className="space-y-2">
            {execution.logs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                No logs available
              </div>
            ) : (
              execution.logs.map((log) => (
                <div key={log.id} className={`p-3 rounded text-sm ${getLogLevelColor(log.level)}`}>
                  <div className="flex items-center justify-between mb-1">
                    <div className="font-medium">
                      {log.level.toUpperCase()}
                      {log.stepId && ` • Step: ${log.stepId}`}
                      {log.agentId && ` • Agent: ${log.agentId}`}
                    </div>
                    <div className="text-xs opacity-75">
                      {new Date(log.timestamp).toLocaleString()}
                    </div>
                  </div>
                  <div>{log.message}</div>
                  {log.data && (
                    <div className="mt-2 bg-black bg-opacity-10 rounded p-2">
                      <pre className="text-xs whitespace-pre-wrap">
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default ExecutionDetails;
