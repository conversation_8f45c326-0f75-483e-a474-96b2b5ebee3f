/**
 * Cost History Component
 * Displays historical cost data and trends
 */

'use client';

import React, { useState, useEffect } from 'react';

interface CostHistoryEntry {
  id: string;
  executionId: string;
  templateName: string;
  timestamp: string;
  totalCost: number;
  tokenUsage: number;
  stepCosts: Record<string, number>;
  modelCosts: Record<string, number>;
  duration: number;
  efficiency: number;
  status: 'completed' | 'failed' | 'running';
}

interface CostTrend {
  period: string;
  totalCost: number;
  totalTokens: number;
  executionCount: number;
  averageCost: number;
  averageTokens: number;
  efficiency: number;
}

interface CostHistoryProps {
  timeRange?: '24h' | '7d' | '30d' | '90d';
  groupBy?: 'day' | 'week' | 'month';
  showTrends?: boolean;
  showBreakdown?: boolean;
  limit?: number;
  className?: string;
}

export function CostHistory({
  timeRange = '30d',
  groupBy = 'day',
  showTrends = true,
  showBreakdown = true,
  limit = 50,
  className = ''
}: CostHistoryProps) {
  const [entries, setEntries] = useState<CostHistoryEntry[]>([]);
  const [trends, setTrends] = useState<CostTrend[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedEntry, setSelectedEntry] = useState<string | null>(null);

  // Fetch cost history
  useEffect(() => {
    const fetchCostHistory = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const params = new URLSearchParams({
          timeRange,
          groupBy,
          limit: limit.toString(),
          includeTrends: showTrends.toString()
        });

        const response = await fetch(`/api/workflow/costs/history?${params}`);
        if (!response.ok) {
          throw new Error('Failed to fetch cost history');
        }

        const data = await response.json();
        setEntries(data.entries || []);
        setTrends(data.trends || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCostHistory();
  }, [timeRange, groupBy, limit, showTrends]);

  const formatCost = (cost: number) => {
    if (cost < 0.01) return `$${(cost * 100).toFixed(2)}¢`;
    return `$${cost.toFixed(4)}`;
  };

  const formatTokens = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(1)}K`;
    return tokens.toString();
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50';
      case 'failed': return 'text-red-600 bg-red-50';
      case 'running': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const calculateSummary = () => {
    const totalCost = entries.reduce((sum, entry) => sum + entry.totalCost, 0);
    const totalTokens = entries.reduce((sum, entry) => sum + entry.tokenUsage, 0);
    const totalExecutions = entries.length;
    const avgCost = totalExecutions > 0 ? totalCost / totalExecutions : 0;
    const avgTokens = totalExecutions > 0 ? totalTokens / totalExecutions : 0;
    const avgEfficiency = totalExecutions > 0 
      ? entries.reduce((sum, entry) => sum + entry.efficiency, 0) / totalExecutions 
      : 0;

    return {
      totalCost,
      totalTokens,
      totalExecutions,
      avgCost,
      avgTokens,
      avgEfficiency
    };
  };

  const summary = calculateSummary();

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Cost History</h3>
          <p className="text-sm text-gray-600">
            {entries.length} executions in the last {timeRange}
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <select
            value={groupBy}
            onChange={(e) => setGroupBy(e.target.value as any)}
            className="px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="day">Group by Day</option>
            <option value="week">Group by Week</option>
            <option value="month">Group by Month</option>
          </select>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">{formatCost(summary.totalCost)}</div>
          <div className="text-sm text-gray-600">Total Cost</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">{formatTokens(summary.totalTokens)}</div>
          <div className="text-sm text-gray-600">Total Tokens</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">{summary.totalExecutions}</div>
          <div className="text-sm text-gray-600">Executions</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-orange-600">{formatCost(summary.avgCost)}</div>
          <div className="text-sm text-gray-600">Avg Cost</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-teal-600">{formatTokens(summary.avgTokens)}</div>
          <div className="text-sm text-gray-600">Avg Tokens</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-indigo-600">{Math.round(summary.avgEfficiency)}%</div>
          <div className="text-sm text-gray-600">Avg Efficiency</div>
        </div>
      </div>

      {/* Trends Chart */}
      {showTrends && trends.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-900 mb-4">Cost Trends</h4>
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="space-y-3">
              {trends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white rounded border">
                  <div>
                    <div className="font-medium text-gray-900">{trend.period}</div>
                    <div className="text-sm text-gray-600">
                      {trend.executionCount} executions
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCost(trend.totalCost)}
                      </div>
                      <div className="text-xs text-gray-600">Total</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCost(trend.averageCost)}
                      </div>
                      <div className="text-xs text-gray-600">Average</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {formatTokens(trend.totalTokens)}
                      </div>
                      <div className="text-xs text-gray-600">Tokens</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-medium text-gray-900">
                        {Math.round(trend.efficiency)}%
                      </div>
                      <div className="text-xs text-gray-600">Efficiency</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Cost History List */}
      <div>
        <h4 className="text-md font-medium text-gray-900 mb-4">Recent Executions</h4>
        <div className="space-y-3">
          {entries.map((entry) => (
            <div
              key={entry.id}
              className={`p-4 border rounded-lg transition-all duration-200 cursor-pointer hover:shadow-md ${
                selectedEntry === entry.id ? 'ring-2 ring-blue-300 bg-blue-50' : 'bg-white'
              }`}
              onClick={() => setSelectedEntry(selectedEntry === entry.id ? null : entry.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(entry.status)}`}>
                    {entry.status.toUpperCase()}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{entry.templateName}</div>
                    <div className="text-sm text-gray-600">
                      {entry.executionId.slice(0, 8)}... • {new Date(entry.timestamp).toLocaleString()}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {formatCost(entry.totalCost)}
                    </div>
                    <div className="text-xs text-gray-600">Cost</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {formatTokens(entry.tokenUsage)}
                    </div>
                    <div className="text-xs text-gray-600">Tokens</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {formatDuration(entry.duration)}
                    </div>
                    <div className="text-xs text-gray-600">Duration</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-medium text-gray-900">
                      {Math.round(entry.efficiency)}%
                    </div>
                    <div className="text-xs text-gray-600">Efficiency</div>
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {selectedEntry === entry.id && showBreakdown && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Step Costs */}
                    {Object.keys(entry.stepCosts).length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-3">Cost by Step</h5>
                        <div className="space-y-2">
                          {Object.entries(entry.stepCosts)
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 5)
                            .map(([step, cost]) => (
                            <div key={step} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm text-gray-700 truncate">{step}</span>
                              <span className="text-sm font-medium text-gray-900">{formatCost(cost)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Model Costs */}
                    {Object.keys(entry.modelCosts).length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-3">Cost by Model</h5>
                        <div className="space-y-2">
                          {Object.entries(entry.modelCosts)
                            .sort(([,a], [,b]) => b - a)
                            .map(([model, cost]) => (
                            <div key={model} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <span className="text-sm text-gray-700">{model}</span>
                              <span className="text-sm font-medium text-gray-900">{formatCost(cost)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Empty State */}
      {entries.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No cost data found for the selected time range
        </div>
      )}
    </div>
  );
}

export default CostHistory;
