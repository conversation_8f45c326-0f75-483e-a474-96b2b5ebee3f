/**
 * Artifact Versioning Component
 * Manages and displays artifact versions with comparison capabilities
 */

'use client';

import React, { useState, useEffect } from 'react';

interface ArtifactVersion {
  id: string;
  artifactId: string;
  version: number;
  name: string;
  type: string;
  content: any;
  size: number;
  createdAt: string;
  executionId: string;
  stepId: string;
  stepName?: string;
  changes?: string[];
  metadata?: Record<string, any>;
  downloadUrl?: string;
  previewUrl?: string;
}

interface ArtifactVersioningProps {
  artifactId: string;
  currentVersion?: number;
  onVersionSelect?: (version: ArtifactVersion) => void;
  showComparison?: boolean;
  showPreview?: boolean;
  className?: string;
}

export function ArtifactVersioning({
  artifactId,
  currentVersion,
  onVersionSelect,
  showComparison = true,
  showPreview = true,
  className = ''
}: ArtifactVersioningProps) {
  const [versions, setVersions] = useState<ArtifactVersion[]>([]);
  const [selectedVersions, setSelectedVersions] = useState<[number, number] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'timeline' | 'comparison'>('list');

  // Fetch artifact versions
  useEffect(() => {
    const fetchVersions = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/artifacts/${artifactId}/versions`);
        if (!response.ok) {
          throw new Error('Failed to fetch artifact versions');
        }

        const data = await response.json();
        setVersions(data.versions || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    if (artifactId) {
      fetchVersions();
    }
  }, [artifactId]);

  const formatSize = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getVersionStatus = (version: ArtifactVersion) => {
    if (version.version === currentVersion) return 'current';
    if (version.version === Math.max(...versions.map(v => v.version))) return 'latest';
    return 'historical';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'current': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'latest': return 'text-green-600 bg-green-50 border-green-200';
      case 'historical': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const handleVersionSelect = (version: ArtifactVersion) => {
    onVersionSelect?.(version);
  };

  const handleComparisonSelect = (versionNumber: number) => {
    if (!selectedVersions) {
      setSelectedVersions([versionNumber, versionNumber]);
    } else if (selectedVersions[0] === versionNumber) {
      setSelectedVersions(null);
    } else {
      const [first] = selectedVersions;
      setSelectedVersions([Math.min(first, versionNumber), Math.max(first, versionNumber)]);
    }
  };

  const getVersionDiff = (v1: ArtifactVersion, v2: ArtifactVersion) => {
    // Simple diff for text content
    if (typeof v1.content === 'string' && typeof v2.content === 'string') {
      const lines1 = v1.content.split('\n');
      const lines2 = v2.content.split('\n');
      const maxLines = Math.max(lines1.length, lines2.length);
      const diff = [];

      for (let i = 0; i < maxLines; i++) {
        const line1 = lines1[i] || '';
        const line2 = lines2[i] || '';
        
        if (line1 !== line2) {
          diff.push({
            lineNumber: i + 1,
            old: line1,
            new: line2,
            type: !line1 ? 'added' : !line2 ? 'removed' : 'modified'
          });
        }
      }
      
      return diff;
    }
    
    return [];
  };

  // List View
  const ListView = () => (
    <div className="space-y-3">
      {versions.map((version) => {
        const status = getVersionStatus(version);
        const isSelected = selectedVersions?.includes(version.version);
        
        return (
          <div
            key={version.id}
            className={`p-4 border rounded-lg transition-all duration-200 cursor-pointer hover:shadow-md ${
              isSelected ? 'ring-2 ring-blue-300 bg-blue-50' : 'bg-white'
            }`}
            onClick={() => handleVersionSelect(version)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {showComparison && (
                  <input
                    type="checkbox"
                    checked={isSelected || false}
                    onChange={(e) => {
                      e.stopPropagation();
                      handleComparisonSelect(version.version);
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                )}
                
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">
                      Version {version.version}
                    </span>
                    <span className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(status)}`}>
                      {status.toUpperCase()}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {version.name} • {version.type}
                  </div>
                  <div className="text-xs text-gray-500">
                    Created: {new Date(version.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatSize(version.size)}
                  </div>
                  <div className="text-xs text-gray-600">Size</div>
                </div>
                
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {version.stepName || version.stepId}
                  </div>
                  <div className="text-xs text-gray-600">Step</div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {version.previewUrl && showPreview && (
                    <a
                      href={version.previewUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 text-blue-600 hover:text-blue-800"
                      onClick={(e) => e.stopPropagation()}
                    >
                      👁️
                    </a>
                  )}
                  {version.downloadUrl && (
                    <a
                      href={version.downloadUrl}
                      download={version.name}
                      className="p-1 text-green-600 hover:text-green-800"
                      onClick={(e) => e.stopPropagation()}
                    >
                      📥
                    </a>
                  )}
                </div>
              </div>
            </div>
            
            {version.changes && version.changes.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-700 mb-2">Changes:</div>
                <ul className="text-sm text-gray-600 space-y-1">
                  {version.changes.map((change, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <span className="text-blue-500">•</span>
                      <span>{change}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            {version.metadata && Object.keys(version.metadata).length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-700 mb-2">Metadata:</div>
                <div className="bg-gray-50 rounded p-2 text-xs">
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(version.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  // Timeline View
  const TimelineView = () => (
    <div className="relative">
      <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-300"></div>
      
      <div className="space-y-6">
        {versions.map((version, index) => {
          const status = getVersionStatus(version);
          
          return (
            <div key={version.id} className="relative">
              <div className={`absolute left-4 w-4 h-4 rounded-full border-2 ${
                status === 'current' ? 'bg-blue-500 border-blue-600' :
                status === 'latest' ? 'bg-green-500 border-green-600' :
                'bg-gray-300 border-gray-400'
              }`}></div>
              
              <div className="ml-12">
                <div
                  className="p-4 bg-white border border-gray-200 rounded-lg cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleVersionSelect(version)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-900">
                        Version {version.version}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(status)}`}>
                        {status.toUpperCase()}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatSize(version.size)}
                    </div>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    {version.name} • {version.type}
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    {new Date(version.createdAt).toLocaleString()} • Step: {version.stepName || version.stepId}
                  </div>
                  
                  {version.changes && version.changes.length > 0 && (
                    <div className="mt-2">
                      <div className="text-xs font-medium text-gray-700 mb-1">Changes:</div>
                      <div className="text-xs text-gray-600">
                        {version.changes.slice(0, 2).join(', ')}
                        {version.changes.length > 2 && ` +${version.changes.length - 2} more`}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );

  // Comparison View
  const ComparisonView = () => {
    if (!selectedVersions || selectedVersions[0] === selectedVersions[1]) {
      return (
        <div className="text-center py-8 text-gray-500">
          Select two different versions to compare
        </div>
      );
    }

    const [v1Number, v2Number] = selectedVersions;
    const v1 = versions.find(v => v.version === v1Number);
    const v2 = versions.find(v => v.version === v2Number);

    if (!v1 || !v2) {
      return (
        <div className="text-center py-8 text-red-500">
          Selected versions not found
        </div>
      );
    }

    const diff = getVersionDiff(v1, v2);

    return (
      <div className="space-y-6">
        {/* Comparison Header */}
        <div className="grid grid-cols-2 gap-4">
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="font-medium text-red-900">Version {v1.version}</div>
            <div className="text-sm text-red-700">{v1.name}</div>
            <div className="text-xs text-red-600">
              {new Date(v1.createdAt).toLocaleString()}
            </div>
          </div>
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="font-medium text-green-900">Version {v2.version}</div>
            <div className="text-sm text-green-700">{v2.name}</div>
            <div className="text-xs text-green-600">
              {new Date(v2.createdAt).toLocaleString()}
            </div>
          </div>
        </div>

        {/* Size Comparison */}
        <div className="p-4 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium text-gray-700 mb-2">Size Comparison</div>
          <div className="flex items-center justify-between">
            <span>Version {v1.version}: {formatSize(v1.size)}</span>
            <span>Version {v2.version}: {formatSize(v2.size)}</span>
          </div>
          <div className="text-xs text-gray-600 mt-1">
            Difference: {formatSize(Math.abs(v2.size - v1.size))} 
            {v2.size > v1.size ? ' larger' : ' smaller'}
          </div>
        </div>

        {/* Content Diff */}
        {diff.length > 0 && (
          <div>
            <div className="text-sm font-medium text-gray-700 mb-3">Content Changes</div>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {diff.map((change, index) => (
                <div key={index} className="text-sm">
                  <div className="text-xs text-gray-500 mb-1">
                    Line {change.lineNumber}
                  </div>
                  {change.type === 'removed' && (
                    <div className="bg-red-50 border-l-4 border-red-400 p-2">
                      <span className="text-red-600">- {change.old}</span>
                    </div>
                  )}
                  {change.type === 'added' && (
                    <div className="bg-green-50 border-l-4 border-green-400 p-2">
                      <span className="text-green-600">+ {change.new}</span>
                    </div>
                  )}
                  {change.type === 'modified' && (
                    <div className="space-y-1">
                      <div className="bg-red-50 border-l-4 border-red-400 p-2">
                        <span className="text-red-600">- {change.old}</span>
                      </div>
                      <div className="bg-green-50 border-l-4 border-green-400 p-2">
                        <span className="text-green-600">+ {change.new}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {diff.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No content differences found
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="space-y-3">
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-red-600">{error}</div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Artifact Versions</h3>
          <p className="text-sm text-gray-600">
            {versions.length} versions available
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="flex border border-gray-300 rounded">
            <button
              onClick={() => setViewMode('list')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              List
            </button>
            <button
              onClick={() => setViewMode('timeline')}
              className={`px-3 py-1 text-sm ${
                viewMode === 'timeline' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Timeline
            </button>
            {showComparison && (
              <button
                onClick={() => setViewMode('comparison')}
                className={`px-3 py-1 text-sm ${
                  viewMode === 'comparison' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                Compare
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'list' && <ListView />}
      {viewMode === 'timeline' && <TimelineView />}
      {viewMode === 'comparison' && <ComparisonView />}

      {/* Empty State */}
      {versions.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          No versions found for this artifact
        </div>
      )}
    </div>
  );
}

export default ArtifactVersioning;
