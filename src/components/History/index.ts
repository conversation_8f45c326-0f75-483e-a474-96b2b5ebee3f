/**
 * History Components
 * Export all workflow history and management components
 */

export { default as WorkflowHistory } from './WorkflowHistory';
export { default as ExecutionList } from './ExecutionList';
export { default as ExecutionDetails } from './ExecutionDetails';
export { default as ArtifactVersioning } from './ArtifactVersioning';
export { default as CostHistory } from './CostHistory';

// Component prop interfaces
export interface WorkflowHistoryProps {
  limit?: number;
  showFilters?: boolean;
  showStats?: boolean;
  onExecutionSelect?: (execution: any) => void;
  className?: string;
}

export interface ExecutionListProps {
  executions: any[];
  onExecutionClick?: (execution: any) => void;
  onExecutionDelete?: (executionId: string) => void;
  showActions?: boolean;
  showCosts?: boolean;
  showProgress?: boolean;
  compact?: boolean;
  className?: string;
}

export interface ExecutionDetailsProps {
  executionId: string;
  onClose?: () => void;
  className?: string;
}

export interface ArtifactVersioningProps {
  artifactId: string;
  currentVersion?: number;
  onVersionSelect?: (version: any) => void;
  showComparison?: boolean;
  showPreview?: boolean;
  className?: string;
}

export interface CostHistoryProps {
  timeRange?: '24h' | '7d' | '30d' | '90d';
  groupBy?: 'day' | 'week' | 'month';
  showTrends?: boolean;
  showBreakdown?: boolean;
  limit?: number;
  className?: string;
}
