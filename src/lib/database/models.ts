/**
 * Database Models and Types
 * TypeScript interfaces for all database entities
 */

// Base model interface
export interface BaseModel {
  id: string;
  created_at: string;
  updated_at: string;
}

// User model
export interface User extends BaseModel {
  email: string;
  name: string;
  role: string;
  preferences: Record<string, any>;
  last_login?: string;
}

// Workflow Template model
export interface WorkflowTemplate extends BaseModel {
  name: string;
  description?: string;
  version: string;
  category: string;
  tags: string[];
  steps: any; // JSONB
  variables: any; // JSONB
  settings: any; // JSONB
  metadata: any; // JSONB
  is_custom: boolean;
  is_active: boolean;
  created_by?: string;
}

// Agent Configuration model
export interface AgentConfig extends BaseModel {
  name: string;
  description?: string;
  type: string;
  model: string;
  provider: string;
  settings: any; // JSONB
  prompts: any; // JSONB
  capabilities: string[];
  constraints: any; // JSONB
  metadata: any; // JSONB
  is_active: boolean;
  is_custom: boolean;
  created_by?: string;
}

// Workflow Execution model
export interface WorkflowExecution extends BaseModel {
  template_id: string;
  template_name: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
  progress: number;
  total_steps: number;
  completed_steps: number;
  failed_steps: number;
  input_variables: any; // JSONB
  output_data: any; // JSONB
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  total_cost: number;
  total_tokens: number;
  created_by?: string;
}

// Workflow Execution Step model
export interface WorkflowExecutionStep extends BaseModel {
  execution_id: string;
  step_id: string;
  step_name: string;
  step_type: string;
  agent_id?: string;
  agent_name?: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped' | 'waiting_approval' | 'waiting_review';
  order_index: number;
  dependencies: string[];
  input_data: any; // JSONB
  output_data: any; // JSONB
  config: any; // JSONB
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  cost: number;
  tokens: number;
  retry_count: number;
}

// Workflow Artifact model
export interface WorkflowArtifact extends BaseModel {
  execution_id: string;
  step_id?: string;
  name: string;
  type: string;
  content_type?: string;
  size_bytes?: number;
  content?: string;
  file_path?: string;
  download_url?: string;
  preview_url?: string;
  metadata: any; // JSONB
  version: number;
  is_final: boolean;
}

// Artifact Version model
export interface ArtifactVersion {
  id: string;
  artifact_id: string;
  version: number;
  content?: string;
  file_path?: string;
  size_bytes?: number;
  changes: string[];
  metadata: any; // JSONB
  created_at: string;
}

// Cost Tracking model
export interface CostTracking {
  id: string;
  execution_id: string;
  step_id?: string;
  agent_id?: string;
  model: string;
  provider: string;
  cost: number;
  input_tokens: number;
  output_tokens: number;
  total_tokens: number;
  request_count: number;
  timestamp: string;
  metadata: any; // JSONB
}

// Workflow Event model
export interface WorkflowEvent {
  id: string;
  execution_id: string;
  step_id?: string;
  event_type: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'critical';
  message: string;
  data: any; // JSONB
  timestamp: string;
}

// User Preferences model
export interface UserPreferences extends BaseModel {
  user_id: string;
  category: string;
  preferences: any; // JSONB
}

// Template Customization model
export interface TemplateCustomization extends BaseModel {
  template_id: string;
  user_id: string;
  name: string;
  description?: string;
  customizations: any; // JSONB
}

// System Config model
export interface SystemConfig extends BaseModel {
  key: string;
  value: any; // JSONB
  type: string;
  category?: string;
  description?: string;
  is_sensitive: boolean;
  requires_restart: boolean;
  updated_by?: string;
}

// Workflow Dependency model
export interface WorkflowDependency {
  id: string;
  execution_id: string;
  step_id: string;
  depends_on: string;
  dependency_type: 'sequential' | 'conditional' | 'parallel' | 'optional';
  condition: any; // JSONB
}

// Performance Metrics model
export interface PerformanceMetric {
  id: string;
  execution_id: string;
  step_id?: string;
  metric_name: string;
  metric_value: number;
  unit?: string;
  timestamp: string;
  metadata: any; // JSONB
}

// Query filters and pagination
export interface PaginationOptions {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export interface WorkflowExecutionFilters extends PaginationOptions {
  status?: string;
  template_id?: string;
  created_by?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CostTrackingFilters extends PaginationOptions {
  execution_id?: string;
  model?: string;
  provider?: string;
  date_from?: string;
  date_to?: string;
}

export interface WorkflowEventFilters extends PaginationOptions {
  execution_id?: string;
  level?: string;
  event_type?: string;
  date_from?: string;
  date_to?: string;
}

// Response types for API
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

export interface WorkflowExecutionWithSteps extends WorkflowExecution {
  steps: WorkflowExecutionStep[];
  artifacts: WorkflowArtifact[];
  events: WorkflowEvent[];
  cost_breakdown: CostTracking[];
}

export interface WorkflowExecutionSummary {
  id: string;
  template_name: string;
  status: string;
  progress: number;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  total_cost: number;
  total_tokens: number;
  created_by?: string;
}

export interface DashboardStats {
  total_executions: number;
  running_executions: number;
  completed_executions: number;
  failed_executions: number;
  total_cost_today: number;
  total_cost_month: number;
  avg_execution_time: number;
  success_rate: number;
}

export interface CostAnalytics {
  total_cost: number;
  cost_by_model: Record<string, number>;
  cost_by_provider: Record<string, number>;
  cost_by_template: Record<string, number>;
  cost_trend: Array<{
    date: string;
    cost: number;
    executions: number;
  }>;
  top_expensive_executions: Array<{
    id: string;
    template_name: string;
    cost: number;
    tokens: number;
  }>;
}

export interface PerformanceAnalytics {
  avg_execution_time: number;
  avg_step_time: number;
  bottleneck_steps: Array<{
    step_name: string;
    avg_duration: number;
    failure_rate: number;
  }>;
  execution_trends: Array<{
    date: string;
    avg_duration: number;
    success_rate: number;
  }>;
  template_performance: Array<{
    template_name: string;
    avg_duration: number;
    success_rate: number;
    usage_count: number;
  }>;
}

// Database operation results
export interface CreateResult {
  id: string;
  created_at: string;
}

export interface UpdateResult {
  id: string;
  updated_at: string;
  affected_rows: number;
}

export interface DeleteResult {
  id: string;
  deleted: boolean;
}

// Validation schemas (for runtime validation)
export interface ValidationSchema {
  required?: string[];
  properties?: Record<string, {
    type: string;
    format?: string;
    minimum?: number;
    maximum?: number;
    pattern?: string;
    enum?: any[];
  }>;
}

// Error types
export class DatabaseError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'DatabaseError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public value?: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends Error {
  constructor(resource: string, id: string) {
    super(`${resource} with id ${id} not found`);
    this.name = 'NotFoundError';
  }
}

// Utility types
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

export type CreateInput<T extends BaseModel> = Omit<T, 'id' | 'created_at' | 'updated_at'>;
export type UpdateInput<T extends BaseModel> = Partial<Omit<T, 'id' | 'created_at' | 'updated_at'>>;

// Export all model names for dynamic operations
export const MODEL_NAMES = {
  USER: 'users',
  WORKFLOW_TEMPLATE: 'workflow_templates',
  AGENT_CONFIG: 'agent_configs',
  WORKFLOW_EXECUTION: 'workflow_executions',
  WORKFLOW_EXECUTION_STEP: 'workflow_execution_steps',
  WORKFLOW_ARTIFACT: 'workflow_artifacts',
  ARTIFACT_VERSION: 'artifact_versions',
  COST_TRACKING: 'cost_tracking',
  WORKFLOW_EVENT: 'workflow_events',
  USER_PREFERENCES: 'user_preferences',
  TEMPLATE_CUSTOMIZATION: 'template_customizations',
  SYSTEM_CONFIG: 'system_configs',
  WORKFLOW_DEPENDENCY: 'workflow_dependencies',
  PERFORMANCE_METRIC: 'performance_metrics'
} as const;
