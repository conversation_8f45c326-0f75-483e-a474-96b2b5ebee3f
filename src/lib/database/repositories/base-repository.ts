/**
 * Base Repository Class
 * Provides common CRUD operations for all entities
 */

import { db, QueryBuilder, createQueryBuilder } from '../connection';
import { 
  BaseModel, 
  PaginationOptions, 
  PaginatedResponse, 
  CreateResult, 
  UpdateResult, 
  DeleteResult,
  CreateInput,
  UpdateInput,
  DatabaseError,
  NotFoundError
} from '../models';

export abstract class BaseRepository<T extends BaseModel> {
  protected tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  // Create
  async create(data: CreateInput<T>): Promise<T> {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = values.map((_, index) => `$${index + 1}`);

      const query = `
        INSERT INTO ${this.tableName} (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
        RETURNING *
      `;

      const result = await db.query<T>(query, values);
      
      if (result.rows.length === 0) {
        throw new DatabaseError('Failed to create record');
      }

      return result.rows[0];
    } catch (error) {
      throw new DatabaseError(
        `Failed to create ${this.tableName} record`,
        error instanceof Error ? error.message : 'Unknown error',
        { data }
      );
    }
  }

  // Read by ID
  async findById(id: string): Promise<T | null> {
    try {
      const query = `SELECT * FROM ${this.tableName} WHERE id = $1`;
      const result = await db.query<T>(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      throw new DatabaseError(
        `Failed to find ${this.tableName} by id`,
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Read by ID with error if not found
  async findByIdOrThrow(id: string): Promise<T> {
    const record = await this.findById(id);
    if (!record) {
      throw new NotFoundError(this.tableName, id);
    }
    return record;
  }

  // Read all with pagination
  async findAll(options: PaginationOptions = {}): Promise<PaginatedResponse<T>> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      // Count total records
      const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName}`;
      const countResult = await db.query(countQuery);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated data
      const dataQuery = `
        SELECT * FROM ${this.tableName}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $1 OFFSET $2
      `;
      const dataResult = await db.query<T>(dataQuery, [limit, offset]);

      return {
        data: dataResult.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };
    } catch (error) {
      throw new DatabaseError(
        `Failed to find all ${this.tableName} records`,
        error instanceof Error ? error.message : 'Unknown error',
        { options }
      );
    }
  }

  // Update
  async update(id: string, data: UpdateInput<T>): Promise<T> {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      
      if (columns.length === 0) {
        throw new DatabaseError('No data provided for update');
      }

      const setClause = columns.map((col, index) => `${col} = $${index + 2}`).join(', ');
      
      const query = `
        UPDATE ${this.tableName}
        SET ${setClause}, updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;

      const result = await db.query<T>(query, [id, ...values]);
      
      if (result.rows.length === 0) {
        throw new NotFoundError(this.tableName, id);
      }

      return result.rows[0];
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new DatabaseError(
        `Failed to update ${this.tableName} record`,
        error instanceof Error ? error.message : 'Unknown error',
        { id, data }
      );
    }
  }

  // Delete
  async delete(id: string): Promise<DeleteResult> {
    try {
      const query = `DELETE FROM ${this.tableName} WHERE id = $1 RETURNING id`;
      const result = await db.query(query, [id]);
      
      if (result.rows.length === 0) {
        throw new NotFoundError(this.tableName, id);
      }

      return {
        id,
        deleted: true
      };
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new DatabaseError(
        `Failed to delete ${this.tableName} record`,
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Soft delete (if the table has a deleted_at column)
  async softDelete(id: string): Promise<T> {
    try {
      const query = `
        UPDATE ${this.tableName}
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id = $1 AND deleted_at IS NULL
        RETURNING *
      `;
      
      const result = await db.query<T>(query, [id]);
      
      if (result.rows.length === 0) {
        throw new NotFoundError(this.tableName, id);
      }

      return result.rows[0];
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new DatabaseError(
        `Failed to soft delete ${this.tableName} record`,
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Find by multiple conditions
  async findWhere(conditions: Record<string, any>, options: PaginationOptions = {}): Promise<PaginatedResponse<T>> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const whereClause = Object.keys(conditions)
        .map((key, index) => `${key} = $${index + 1}`)
        .join(' AND ');
      
      const values = Object.values(conditions);

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM ${this.tableName} 
        ${whereClause ? `WHERE ${whereClause}` : ''}
      `;
      const countResult = await db.query(countQuery, values);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated data
      const dataQuery = `
        SELECT * FROM ${this.tableName}
        ${whereClause ? `WHERE ${whereClause}` : ''}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $${values.length + 1} OFFSET $${values.length + 2}
      `;
      const dataResult = await db.query<T>(dataQuery, [...values, limit, offset]);

      return {
        data: dataResult.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };
    } catch (error) {
      throw new DatabaseError(
        `Failed to find ${this.tableName} records with conditions`,
        error instanceof Error ? error.message : 'Unknown error',
        { conditions, options }
      );
    }
  }

  // Find one by conditions
  async findOneWhere(conditions: Record<string, any>): Promise<T | null> {
    try {
      const whereClause = Object.keys(conditions)
        .map((key, index) => `${key} = $${index + 1}`)
        .join(' AND ');
      
      const values = Object.values(conditions);

      const query = `
        SELECT * FROM ${this.tableName}
        WHERE ${whereClause}
        LIMIT 1
      `;

      const result = await db.query<T>(query, values);
      return result.rows[0] || null;
    } catch (error) {
      throw new DatabaseError(
        `Failed to find one ${this.tableName} record`,
        error instanceof Error ? error.message : 'Unknown error',
        { conditions }
      );
    }
  }

  // Exists check
  async exists(id: string): Promise<boolean> {
    try {
      const query = `SELECT 1 FROM ${this.tableName} WHERE id = $1 LIMIT 1`;
      const result = await db.query(query, [id]);
      return result.rows.length > 0;
    } catch (error) {
      throw new DatabaseError(
        `Failed to check if ${this.tableName} exists`,
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Count records
  async count(conditions?: Record<string, any>): Promise<number> {
    try {
      let query = `SELECT COUNT(*) as total FROM ${this.tableName}`;
      let values: any[] = [];

      if (conditions && Object.keys(conditions).length > 0) {
        const whereClause = Object.keys(conditions)
          .map((key, index) => `${key} = $${index + 1}`)
          .join(' AND ');
        query += ` WHERE ${whereClause}`;
        values = Object.values(conditions);
      }

      const result = await db.query(query, values);
      return parseInt(result.rows[0].total);
    } catch (error) {
      throw new DatabaseError(
        `Failed to count ${this.tableName} records`,
        error instanceof Error ? error.message : 'Unknown error',
        { conditions }
      );
    }
  }

  // Batch operations
  async createMany(records: CreateInput<T>[]): Promise<T[]> {
    if (records.length === 0) return [];

    try {
      return await db.transaction(async (client) => {
        const results: T[] = [];
        
        for (const record of records) {
          const columns = Object.keys(record);
          const values = Object.values(record);
          const placeholders = values.map((_, index) => `$${index + 1}`);

          const query = `
            INSERT INTO ${this.tableName} (${columns.join(', ')})
            VALUES (${placeholders.join(', ')})
            RETURNING *
          `;

          const result = await client.query<T>(query, values);
          if (result.rows[0]) {
            results.push(result.rows[0]);
          }
        }

        return results;
      });
    } catch (error) {
      throw new DatabaseError(
        `Failed to create multiple ${this.tableName} records`,
        error instanceof Error ? error.message : 'Unknown error',
        { count: records.length }
      );
    }
  }

  // Search functionality
  async search(searchTerm: string, searchColumns: string[], options: PaginationOptions = {}): Promise<PaginatedResponse<T>> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options;

      const searchConditions = searchColumns
        .map((column, index) => `${column}::text ILIKE $${index + 1}`)
        .join(' OR ');
      
      const searchValue = `%${searchTerm}%`;
      const searchValues = searchColumns.map(() => searchValue);

      // Count total records
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM ${this.tableName} 
        WHERE ${searchConditions}
      `;
      const countResult = await db.query(countQuery, searchValues);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated data
      const dataQuery = `
        SELECT * FROM ${this.tableName}
        WHERE ${searchConditions}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $${searchValues.length + 1} OFFSET $${searchValues.length + 2}
      `;
      const dataResult = await db.query<T>(dataQuery, [...searchValues, limit, offset]);

      return {
        data: dataResult.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };
    } catch (error) {
      throw new DatabaseError(
        `Failed to search ${this.tableName} records`,
        error instanceof Error ? error.message : 'Unknown error',
        { searchTerm, searchColumns, options }
      );
    }
  }

  // Get query builder for complex queries
  protected getQueryBuilder(): QueryBuilder {
    return createQueryBuilder();
  }

  // Execute raw query
  protected async executeQuery<R = any>(query: string, params?: any[]): Promise<R[]> {
    try {
      const result = await db.query<R>(query, params);
      return result.rows;
    } catch (error) {
      throw new DatabaseError(
        `Failed to execute query on ${this.tableName}`,
        error instanceof Error ? error.message : 'Unknown error',
        { query, params }
      );
    }
  }
}
