/**
 * Workflow Repository
 * Data access layer for workflow executions and related entities
 */

import { BaseRepository } from './base-repository';
import { db } from '../connection';
import {
  WorkflowExecution,
  WorkflowExecutionStep,
  WorkflowArtifact,
  WorkflowEvent,
  CostTracking,
  WorkflowExecutionFilters,
  PaginatedResponse,
  WorkflowExecutionWithSteps,
  WorkflowExecutionSummary,
  DashboardStats,
  CostAnalytics,
  PerformanceAnalytics,
  DatabaseError
} from '../models';

export class WorkflowExecutionRepository extends BaseRepository<WorkflowExecution> {
  constructor() {
    super('workflow_executions');
  }

  // Get executions with filters
  async findWithFilters(filters: WorkflowExecutionFilters): Promise<PaginatedResponse<WorkflowExecution>> {
    try {
      const {
        limit = 50,
        offset = 0,
        sortBy = 'created_at',
        sortOrder = 'DESC',
        status,
        template_id,
        created_by,
        date_from,
        date_to,
        search
      } = filters;

      let whereConditions: string[] = [];
      let params: any[] = [];
      let paramIndex = 1;

      // Build WHERE conditions
      if (status) {
        whereConditions.push(`status = $${paramIndex++}`);
        params.push(status);
      }

      if (template_id) {
        whereConditions.push(`template_id = $${paramIndex++}`);
        params.push(template_id);
      }

      if (created_by) {
        whereConditions.push(`created_by = $${paramIndex++}`);
        params.push(created_by);
      }

      if (date_from) {
        whereConditions.push(`created_at >= $${paramIndex++}`);
        params.push(date_from);
      }

      if (date_to) {
        whereConditions.push(`created_at <= $${paramIndex++}`);
        params.push(date_to);
      }

      if (search) {
        whereConditions.push(`(template_name ILIKE $${paramIndex++} OR id::text ILIKE $${paramIndex++})`);
        params.push(`%${search}%`, `%${search}%`);
        paramIndex++; // Account for the second parameter
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // Count total records
      const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
      const countResult = await db.query(countQuery, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated data
      const dataQuery = `
        SELECT * FROM ${this.tableName}
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT $${paramIndex++} OFFSET $${paramIndex++}
      `;
      const dataResult = await db.query<WorkflowExecution>(dataQuery, [...params, limit, offset]);

      return {
        data: dataResult.rows,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      };
    } catch (error) {
      throw new DatabaseError(
        'Failed to find workflow executions with filters',
        error instanceof Error ? error.message : 'Unknown error',
        { filters }
      );
    }
  }

  // Get execution with all related data
  async findByIdWithDetails(id: string): Promise<WorkflowExecutionWithSteps | null> {
    try {
      const execution = await this.findById(id);
      if (!execution) return null;

      // Get steps
      const stepsQuery = `
        SELECT * FROM workflow_execution_steps 
        WHERE execution_id = $1 
        ORDER BY order_index ASC
      `;
      const stepsResult = await db.query<WorkflowExecutionStep>(stepsQuery, [id]);

      // Get artifacts
      const artifactsQuery = `
        SELECT * FROM workflow_artifacts 
        WHERE execution_id = $1 
        ORDER BY created_at DESC
      `;
      const artifactsResult = await db.query<WorkflowArtifact>(artifactsQuery, [id]);

      // Get events
      const eventsQuery = `
        SELECT * FROM workflow_events 
        WHERE execution_id = $1 
        ORDER BY timestamp DESC
        LIMIT 100
      `;
      const eventsResult = await db.query<WorkflowEvent>(eventsQuery, [id]);

      // Get cost breakdown
      const costQuery = `
        SELECT * FROM cost_tracking 
        WHERE execution_id = $1 
        ORDER BY timestamp DESC
      `;
      const costResult = await db.query<CostTracking>(costQuery, [id]);

      return {
        ...execution,
        steps: stepsResult.rows,
        artifacts: artifactsResult.rows,
        events: eventsResult.rows,
        cost_breakdown: costResult.rows
      };
    } catch (error) {
      throw new DatabaseError(
        'Failed to find workflow execution with details',
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Update execution status and progress
  async updateStatus(id: string, status: WorkflowExecution['status'], progress?: number): Promise<WorkflowExecution> {
    try {
      const updateData: any = { status };
      
      if (progress !== undefined) {
        updateData.progress = progress;
      }

      if (status === 'running' && !await this.hasStartTime(id)) {
        updateData.started_at = new Date().toISOString();
      }

      if (status === 'completed' || status === 'failed' || status === 'cancelled') {
        updateData.completed_at = new Date().toISOString();
        
        // Calculate duration if we have start time
        const execution = await this.findById(id);
        if (execution?.started_at) {
          const startTime = new Date(execution.started_at).getTime();
          const endTime = new Date().getTime();
          updateData.duration_ms = endTime - startTime;
        }
      }

      return await this.update(id, updateData);
    } catch (error) {
      throw new DatabaseError(
        'Failed to update workflow execution status',
        error instanceof Error ? error.message : 'Unknown error',
        { id, status, progress }
      );
    }
  }

  // Update step counts
  async updateStepCounts(id: string): Promise<WorkflowExecution> {
    try {
      const query = `
        UPDATE workflow_executions 
        SET 
          completed_steps = (
            SELECT COUNT(*) FROM workflow_execution_steps 
            WHERE execution_id = $1 AND status = 'completed'
          ),
          failed_steps = (
            SELECT COUNT(*) FROM workflow_execution_steps 
            WHERE execution_id = $1 AND status = 'failed'
          ),
          progress = CASE 
            WHEN total_steps > 0 THEN 
              ROUND((
                SELECT COUNT(*) FROM workflow_execution_steps 
                WHERE execution_id = $1 AND status IN ('completed', 'skipped')
              ) * 100.0 / total_steps)
            ELSE 0 
          END,
          updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;

      const result = await db.query<WorkflowExecution>(query, [id]);
      
      if (result.rows.length === 0) {
        throw new Error(`Workflow execution ${id} not found`);
      }

      return result.rows[0];
    } catch (error) {
      throw new DatabaseError(
        'Failed to update workflow execution step counts',
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Update cost totals
  async updateCostTotals(id: string): Promise<WorkflowExecution> {
    try {
      const query = `
        UPDATE workflow_executions 
        SET 
          total_cost = COALESCE((
            SELECT SUM(cost) FROM cost_tracking 
            WHERE execution_id = $1
          ), 0),
          total_tokens = COALESCE((
            SELECT SUM(total_tokens) FROM cost_tracking 
            WHERE execution_id = $1
          ), 0),
          updated_at = NOW()
        WHERE id = $1
        RETURNING *
      `;

      const result = await db.query<WorkflowExecution>(query, [id]);
      
      if (result.rows.length === 0) {
        throw new Error(`Workflow execution ${id} not found`);
      }

      return result.rows[0];
    } catch (error) {
      throw new DatabaseError(
        'Failed to update workflow execution cost totals',
        error instanceof Error ? error.message : 'Unknown error',
        { id }
      );
    }
  }

  // Get dashboard statistics
  async getDashboardStats(userId?: string): Promise<DashboardStats> {
    try {
      const userFilter = userId ? 'WHERE created_by = $1' : '';
      const params = userId ? [userId] : [];

      const query = `
        SELECT 
          COUNT(*) as total_executions,
          COUNT(*) FILTER (WHERE status = 'running') as running_executions,
          COUNT(*) FILTER (WHERE status = 'completed') as completed_executions,
          COUNT(*) FILTER (WHERE status = 'failed') as failed_executions,
          COALESCE(SUM(total_cost) FILTER (WHERE DATE(created_at) = CURRENT_DATE), 0) as total_cost_today,
          COALESCE(SUM(total_cost) FILTER (WHERE DATE_TRUNC('month', created_at) = DATE_TRUNC('month', CURRENT_DATE)), 0) as total_cost_month,
          COALESCE(AVG(duration_ms) FILTER (WHERE status = 'completed' AND duration_ms IS NOT NULL), 0) as avg_execution_time,
          CASE 
            WHEN COUNT(*) > 0 THEN 
              ROUND(COUNT(*) FILTER (WHERE status = 'completed') * 100.0 / COUNT(*), 2)
            ELSE 0 
          END as success_rate
        FROM workflow_executions
        ${userFilter}
      `;

      const result = await db.query(query, params);
      return result.rows[0];
    } catch (error) {
      throw new DatabaseError(
        'Failed to get dashboard statistics',
        error instanceof Error ? error.message : 'Unknown error',
        { userId }
      );
    }
  }

  // Get recent executions summary
  async getRecentExecutions(limit = 10, userId?: string): Promise<WorkflowExecutionSummary[]> {
    try {
      const userFilter = userId ? 'WHERE created_by = $2' : '';
      const params = userId ? [limit, userId] : [limit];

      const query = `
        SELECT 
          id,
          template_name,
          status,
          progress,
          started_at,
          completed_at,
          duration_ms,
          total_cost,
          total_tokens,
          created_by
        FROM workflow_executions
        ${userFilter}
        ORDER BY created_at DESC
        LIMIT $1
      `;

      const result = await db.query<WorkflowExecutionSummary>(query, params);
      return result.rows;
    } catch (error) {
      throw new DatabaseError(
        'Failed to get recent executions',
        error instanceof Error ? error.message : 'Unknown error',
        { limit, userId }
      );
    }
  }

  // Helper methods
  private async hasStartTime(id: string): Promise<boolean> {
    const query = `SELECT started_at FROM workflow_executions WHERE id = $1`;
    const result = await db.query(query, [id]);
    return result.rows[0]?.started_at != null;
  }

  // Get executions by template
  async findByTemplateId(templateId: string, options: { limit?: number; offset?: number } = {}): Promise<PaginatedResponse<WorkflowExecution>> {
    return this.findWhere({ template_id: templateId }, options);
  }

  // Get running executions
  async findRunning(userId?: string): Promise<WorkflowExecution[]> {
    const conditions: any = { status: 'running' };
    if (userId) {
      conditions.created_by = userId;
    }
    
    const result = await this.findWhere(conditions, { limit: 100 });
    return result.data;
  }

  // Cancel execution
  async cancel(id: string, reason?: string): Promise<WorkflowExecution> {
    const updateData: any = { 
      status: 'cancelled' as const,
      completed_at: new Date().toISOString()
    };

    if (reason) {
      updateData.error_message = reason;
    }

    return await this.update(id, updateData);
  }
}

// Export singleton instance
export const workflowExecutionRepository = new WorkflowExecutionRepository();
