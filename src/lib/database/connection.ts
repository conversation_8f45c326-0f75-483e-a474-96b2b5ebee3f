/**
 * Database Connection and Configuration
 * Handles PostgreSQL connection with connection pooling
 */

import { Pool, PoolClient, QueryResult } from 'pg';

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
  maxConnections?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

class DatabaseManager {
  private static instance: DatabaseManager;
  private pool: Pool | null = null;
  private config: DatabaseConfig;

  private constructor() {
    this.config = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'authenciocms',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      ssl: process.env.DB_SSL === 'true',
      maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
      idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),
      connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '2000')
    };
  }

  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  async connect(): Promise<void> {
    if (this.pool) {
      return; // Already connected
    }

    try {
      this.pool = new Pool({
        host: this.config.host,
        port: this.config.port,
        database: this.config.database,
        user: this.config.user,
        password: this.config.password,
        ssl: this.config.ssl ? { rejectUnauthorized: false } : false,
        max: this.config.maxConnections,
        idleTimeoutMillis: this.config.idleTimeoutMillis,
        connectionTimeoutMillis: this.config.connectionTimeoutMillis
      });

      // Test connection
      const client = await this.pool.connect();
      await client.query('SELECT NOW()');
      client.release();

      console.log('Database connected successfully');
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.pool) {
      await this.pool.end();
      this.pool = null;
      console.log('Database disconnected');
    }
  }

  async query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
    if (!this.pool) {
      throw new Error('Database not connected');
    }

    try {
      const start = Date.now();
      const result = await this.pool.query<T>(text, params);
      const duration = Date.now() - start;
      
      if (process.env.NODE_ENV === 'development') {
        console.log('Query executed:', { text, duration: `${duration}ms`, rows: result.rowCount });
      }
      
      return result;
    } catch (error) {
      console.error('Query error:', { text, params, error });
      throw error;
    }
  }

  async getClient(): Promise<PoolClient> {
    if (!this.pool) {
      throw new Error('Database not connected');
    }
    return this.pool.connect();
  }

  async transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
    const client = await this.getClient();
    
    try {
      await client.query('BEGIN');
      const result = await callback(client);
      await client.query('COMMIT');
      return result;
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  // Health check
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: any }> {
    try {
      if (!this.pool) {
        return { status: 'unhealthy', details: { error: 'Pool not initialized' } };
      }

      const start = Date.now();
      const result = await this.pool.query('SELECT NOW() as timestamp, version() as version');
      const responseTime = Date.now() - start;

      return {
        status: 'healthy',
        details: {
          responseTime: `${responseTime}ms`,
          timestamp: result.rows[0].timestamp,
          version: result.rows[0].version,
          totalConnections: this.pool.totalCount,
          idleConnections: this.pool.idleCount,
          waitingConnections: this.pool.waitingCount
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  // Migration support
  async runMigration(migrationSql: string): Promise<void> {
    try {
      await this.query(migrationSql);
      console.log('Migration executed successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  // Backup and restore helpers
  async backup(tables?: string[]): Promise<any[]> {
    const tablesToBackup = tables || await this.getAllTableNames();
    const backup: any[] = [];

    for (const table of tablesToBackup) {
      try {
        const result = await this.query(`SELECT * FROM ${table}`);
        backup.push({
          table,
          data: result.rows,
          count: result.rowCount
        });
      } catch (error) {
        console.error(`Failed to backup table ${table}:`, error);
      }
    }

    return backup;
  }

  private async getAllTableNames(): Promise<string[]> {
    const result = await this.query(`
      SELECT tablename 
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY tablename
    `);
    return result.rows.map(row => row.tablename);
  }

  // Performance monitoring
  async getConnectionStats(): Promise<any> {
    if (!this.pool) {
      return null;
    }

    return {
      totalConnections: this.pool.totalCount,
      idleConnections: this.pool.idleCount,
      waitingConnections: this.pool.waitingCount,
      maxConnections: this.config.maxConnections
    };
  }

  async getSlowQueries(limit = 10): Promise<any[]> {
    try {
      const result = await this.query(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows
        FROM pg_stat_statements 
        ORDER BY mean_time DESC 
        LIMIT $1
      `, [limit]);
      
      return result.rows;
    } catch (error) {
      // pg_stat_statements extension might not be enabled
      console.warn('Could not fetch slow queries - pg_stat_statements extension may not be enabled');
      return [];
    }
  }
}

// Export singleton instance
export const db = DatabaseManager.getInstance();

// Export types
export type { DatabaseConfig, QueryResult, PoolClient };

// Utility functions
export async function initializeDatabase(): Promise<void> {
  await db.connect();
}

export async function closeDatabase(): Promise<void> {
  await db.disconnect();
}

// Query builder helpers
export class QueryBuilder {
  private query: string = '';
  private params: any[] = [];
  private paramCount: number = 0;

  select(columns: string | string[]): this {
    const cols = Array.isArray(columns) ? columns.join(', ') : columns;
    this.query = `SELECT ${cols}`;
    return this;
  }

  from(table: string): this {
    this.query += ` FROM ${table}`;
    return this;
  }

  where(condition: string, value?: any): this {
    const operator = this.query.includes('WHERE') ? 'AND' : 'WHERE';
    
    if (value !== undefined) {
      this.paramCount++;
      this.query += ` ${operator} ${condition.replace('?', `$${this.paramCount}`)}`;
      this.params.push(value);
    } else {
      this.query += ` ${operator} ${condition}`;
    }
    
    return this;
  }

  orderBy(column: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    this.query += ` ORDER BY ${column} ${direction}`;
    return this;
  }

  limit(count: number): this {
    this.paramCount++;
    this.query += ` LIMIT $${this.paramCount}`;
    this.params.push(count);
    return this;
  }

  offset(count: number): this {
    this.paramCount++;
    this.query += ` OFFSET $${this.paramCount}`;
    this.params.push(count);
    return this;
  }

  build(): { query: string; params: any[] } {
    return { query: this.query, params: this.params };
  }

  async execute<T = any>(): Promise<QueryResult<T>> {
    return db.query<T>(this.query, this.params);
  }
}

export function createQueryBuilder(): QueryBuilder {
  return new QueryBuilder();
}
