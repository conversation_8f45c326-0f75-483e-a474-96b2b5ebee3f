/**
 * Agent Configuration Manager
 * Manages AI agent settings and configurations
 */

export interface AgentConfig {
  id: string;
  name: string;
  description?: string;
  type: 'content' | 'seo' | 'research' | 'review' | 'analysis' | 'custom';
  model: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  settings: AgentSettings;
  prompts: AgentPrompts;
  capabilities: string[];
  constraints: AgentConstraints;
  metadata: AgentMetadata;
  isActive: boolean;
  isCustom: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
}

export interface AgentSettings {
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  timeout: number;
  retryCount: number;
  costLimit?: number;
  rateLimitRpm?: number;
  rateLimitTpm?: number;
}

export interface AgentPrompts {
  system: string;
  userTemplate: string;
  examples?: PromptExample[];
  instructions?: string[];
  constraints?: string[];
  outputFormat?: string;
}

export interface PromptExample {
  input: string;
  output: string;
  description?: string;
}

export interface AgentConstraints {
  maxCostPerRequest?: number;
  maxTokensPerRequest?: number;
  allowedDomains?: string[];
  blockedDomains?: string[];
  contentFilters?: string[];
  requireApproval?: boolean;
  sensitiveDataHandling?: 'allow' | 'mask' | 'reject';
}

export interface AgentMetadata {
  version: string;
  usageCount: number;
  successRate: number;
  averageResponseTime: number;
  averageCost: number;
  lastUsed?: string;
  tags: string[];
  category?: string;
}

export interface AgentProfile {
  id: string;
  name: string;
  description: string;
  agentConfigs: string[]; // Agent config IDs
  settings: {
    defaultModel: string;
    defaultProvider: string;
    globalConstraints: AgentConstraints;
  };
  createdAt: string;
  updatedAt: string;
}

export class AgentConfigManager {
  private static instance: AgentConfigManager;
  private agents: Map<string, AgentConfig> = new Map();
  private profiles: Map<string, AgentProfile> = new Map();
  private modelCapabilities: Map<string, string[]> = new Map();

  private constructor() {
    this.initializeModelCapabilities();
    this.initializeDefaultAgents();
  }

  static getInstance(): AgentConfigManager {
    if (!AgentConfigManager.instance) {
      AgentConfigManager.instance = new AgentConfigManager();
    }
    return AgentConfigManager.instance;
  }

  private initializeModelCapabilities() {
    this.modelCapabilities.set('gpt-3.5-turbo', [
      'text-generation', 'conversation', 'analysis', 'summarization'
    ]);
    this.modelCapabilities.set('gpt-4', [
      'text-generation', 'conversation', 'analysis', 'summarization', 'reasoning', 'code-generation'
    ]);
    this.modelCapabilities.set('gpt-4-turbo', [
      'text-generation', 'conversation', 'analysis', 'summarization', 'reasoning', 'code-generation', 'vision'
    ]);
    this.modelCapabilities.set('claude-3-sonnet', [
      'text-generation', 'conversation', 'analysis', 'summarization', 'reasoning', 'code-generation'
    ]);
    this.modelCapabilities.set('claude-3-opus', [
      'text-generation', 'conversation', 'analysis', 'summarization', 'reasoning', 'code-generation', 'creative-writing'
    ]);
  }

  private initializeDefaultAgents() {
    const defaultAgents: AgentConfig[] = [
      {
        id: 'content-writer',
        name: 'Content Writer',
        description: 'Specialized in creating high-quality written content',
        type: 'content',
        model: 'gpt-4',
        provider: 'openai',
        settings: {
          temperature: 0.7,
          maxTokens: 4000,
          topP: 0.9,
          timeout: 300,
          retryCount: 2,
          costLimit: 1.0
        },
        prompts: {
          system: 'You are a professional content writer specializing in creating engaging, well-structured, and informative content. Focus on clarity, readability, and audience engagement.',
          userTemplate: 'Write content about: {topic}\nTarget audience: {audience}\nTone: {tone}\nWord count: {wordCount}',
          instructions: [
            'Use clear and engaging language',
            'Structure content with proper headings',
            'Include relevant examples and details',
            'Maintain consistent tone throughout'
          ],
          outputFormat: 'Markdown format with proper headings and structure'
        },
        capabilities: ['content-creation', 'copywriting', 'blog-writing', 'article-writing'],
        constraints: {
          maxCostPerRequest: 0.5,
          maxTokensPerRequest: 4000,
          contentFilters: ['inappropriate', 'harmful'],
          sensitiveDataHandling: 'mask'
        },
        metadata: {
          version: '1.0.0',
          usageCount: 0,
          successRate: 0.95,
          averageResponseTime: 15000,
          averageCost: 0.12,
          tags: ['content', 'writing', 'blog'],
          category: 'Content Creation'
        },
        isActive: true,
        isCustom: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'seo-specialist',
        name: 'SEO Specialist',
        description: 'Expert in search engine optimization and keyword research',
        type: 'seo',
        model: 'gpt-4',
        provider: 'openai',
        settings: {
          temperature: 0.3,
          maxTokens: 2000,
          timeout: 240,
          retryCount: 3,
          costLimit: 0.8
        },
        prompts: {
          system: 'You are an SEO specialist with expertise in keyword research, content optimization, and search engine best practices. Provide data-driven recommendations.',
          userTemplate: 'Analyze SEO for: {topic}\nTarget keywords: {keywords}\nCompetitor analysis: {includeCompetitors}',
          instructions: [
            'Focus on search intent and user experience',
            'Provide specific, actionable recommendations',
            'Include keyword density and placement suggestions',
            'Consider technical SEO factors'
          ],
          outputFormat: 'Structured analysis with clear recommendations and metrics'
        },
        capabilities: ['keyword-research', 'seo-analysis', 'content-optimization', 'competitor-analysis'],
        constraints: {
          maxCostPerRequest: 0.3,
          maxTokensPerRequest: 2000,
          requireApproval: false
        },
        metadata: {
          version: '1.0.0',
          usageCount: 0,
          successRate: 0.92,
          averageResponseTime: 12000,
          averageCost: 0.08,
          tags: ['seo', 'keywords', 'optimization'],
          category: 'SEO & Marketing'
        },
        isActive: true,
        isCustom: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'content-reviewer',
        name: 'Content Reviewer',
        description: 'Reviews content for quality, accuracy, and compliance',
        type: 'review',
        model: 'gpt-4',
        provider: 'openai',
        settings: {
          temperature: 0.2,
          maxTokens: 3000,
          timeout: 180,
          retryCount: 2,
          costLimit: 0.6
        },
        prompts: {
          system: 'You are a meticulous content reviewer focused on quality, accuracy, grammar, and adherence to guidelines. Provide constructive feedback and specific improvement suggestions.',
          userTemplate: 'Review this content: {content}\nGuidelines: {guidelines}\nFocus areas: {focusAreas}',
          instructions: [
            'Check for grammatical and spelling errors',
            'Verify factual accuracy where possible',
            'Ensure consistency in tone and style',
            'Provide specific, actionable feedback'
          ],
          outputFormat: 'Detailed review with categorized feedback and improvement suggestions'
        },
        capabilities: ['content-review', 'grammar-check', 'fact-checking', 'style-analysis'],
        constraints: {
          maxCostPerRequest: 0.4,
          maxTokensPerRequest: 3000,
          contentFilters: ['inappropriate', 'harmful', 'misleading']
        },
        metadata: {
          version: '1.0.0',
          usageCount: 0,
          successRate: 0.97,
          averageResponseTime: 10000,
          averageCost: 0.10,
          tags: ['review', 'quality', 'editing'],
          category: 'Quality Assurance'
        },
        isActive: true,
        isCustom: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    defaultAgents.forEach(agent => {
      this.agents.set(agent.id, agent);
    });
  }

  // Agent CRUD operations
  async getAgent(id: string): Promise<AgentConfig | null> {
    return this.agents.get(id) || null;
  }

  async getAllAgents(): Promise<AgentConfig[]> {
    return Array.from(this.agents.values());
  }

  async getActiveAgents(): Promise<AgentConfig[]> {
    return Array.from(this.agents.values()).filter(agent => agent.isActive);
  }

  async getAgentsByType(type: AgentConfig['type']): Promise<AgentConfig[]> {
    return Array.from(this.agents.values()).filter(agent => agent.type === type);
  }

  async getAgentsByCapability(capability: string): Promise<AgentConfig[]> {
    return Array.from(this.agents.values())
      .filter(agent => agent.capabilities.includes(capability));
  }

  async createAgent(agent: Omit<AgentConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<AgentConfig> {
    const id = this.generateAgentId(agent.name);
    const now = new Date().toISOString();
    
    const newAgent: AgentConfig = {
      ...agent,
      id,
      createdAt: now,
      updatedAt: now,
      isCustom: true,
      metadata: {
        ...agent.metadata,
        usageCount: 0,
        successRate: 0,
        averageResponseTime: 0,
        averageCost: 0
      }
    };

    // Validate agent configuration
    const validation = this.validateAgent(newAgent);
    if (!validation.valid) {
      throw new Error(`Invalid agent configuration: ${validation.errors.join(', ')}`);
    }

    this.agents.set(id, newAgent);
    await this.persistAgent(newAgent);
    return newAgent;
  }

  async updateAgent(id: string, updates: Partial<AgentConfig>): Promise<AgentConfig | null> {
    const agent = this.agents.get(id);
    if (!agent) return null;

    const updatedAgent: AgentConfig = {
      ...agent,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    // Validate updated configuration
    const validation = this.validateAgent(updatedAgent);
    if (!validation.valid) {
      throw new Error(`Invalid agent configuration: ${validation.errors.join(', ')}`);
    }

    this.agents.set(id, updatedAgent);
    await this.persistAgent(updatedAgent);
    return updatedAgent;
  }

  async deleteAgent(id: string): Promise<boolean> {
    const agent = this.agents.get(id);
    if (!agent || !agent.isCustom) {
      return false; // Can't delete built-in agents
    }

    this.agents.delete(id);
    await this.removePersistedAgent(id);
    return true;
  }

  async duplicateAgent(id: string, newName: string): Promise<AgentConfig | null> {
    const agent = this.agents.get(id);
    if (!agent) return null;

    const duplicated = await this.createAgent({
      ...agent,
      name: newName,
      isCustom: true
    });

    return duplicated;
  }

  // Agent validation
  validateAgent(agent: AgentConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Basic validation
    if (!agent.name?.trim()) {
      errors.push('Agent name is required');
    }
    if (!agent.model?.trim()) {
      errors.push('Agent model is required');
    }
    if (!agent.provider?.trim()) {
      errors.push('Agent provider is required');
    }

    // Settings validation
    if (agent.settings.temperature < 0 || agent.settings.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }
    if (agent.settings.maxTokens < 1 || agent.settings.maxTokens > 32000) {
      errors.push('Max tokens must be between 1 and 32000');
    }
    if (agent.settings.timeout < 1 || agent.settings.timeout > 3600) {
      errors.push('Timeout must be between 1 and 3600 seconds');
    }

    // Prompts validation
    if (!agent.prompts.system?.trim()) {
      errors.push('System prompt is required');
    }
    if (!agent.prompts.userTemplate?.trim()) {
      errors.push('User template is required');
    }

    // Model capability validation
    const modelCapabilities = this.modelCapabilities.get(agent.model) || [];
    const requiredCapabilities = agent.capabilities.filter(cap => 
      !modelCapabilities.includes(cap) && !cap.startsWith('custom-')
    );
    if (requiredCapabilities.length > 0) {
      errors.push(`Model ${agent.model} doesn't support capabilities: ${requiredCapabilities.join(', ')}`);
    }

    return { valid: errors.length === 0, errors };
  }

  // Agent profiles
  async createProfile(profile: Omit<AgentProfile, 'createdAt' | 'updatedAt'>): Promise<AgentProfile> {
    const now = new Date().toISOString();
    const newProfile: AgentProfile = {
      ...profile,
      createdAt: now,
      updatedAt: now
    };

    this.profiles.set(profile.id, newProfile);
    await this.persistProfile(newProfile);
    return newProfile;
  }

  async getProfile(id: string): Promise<AgentProfile | null> {
    return this.profiles.get(id) || null;
  }

  async getAllProfiles(): Promise<AgentProfile[]> {
    return Array.from(this.profiles.values());
  }

  // Agent metrics and monitoring
  async updateAgentMetrics(id: string, metrics: {
    responseTime?: number;
    cost?: number;
    success?: boolean;
  }): Promise<void> {
    const agent = this.agents.get(id);
    if (!agent) return;

    const metadata = agent.metadata;
    metadata.usageCount += 1;
    metadata.lastUsed = new Date().toISOString();

    if (metrics.responseTime !== undefined) {
      metadata.averageResponseTime = (
        (metadata.averageResponseTime * (metadata.usageCount - 1) + metrics.responseTime) / 
        metadata.usageCount
      );
    }

    if (metrics.cost !== undefined) {
      metadata.averageCost = (
        (metadata.averageCost * (metadata.usageCount - 1) + metrics.cost) / 
        metadata.usageCount
      );
    }

    if (metrics.success !== undefined) {
      const successCount = Math.round(metadata.successRate * (metadata.usageCount - 1));
      const newSuccessCount = successCount + (metrics.success ? 1 : 0);
      metadata.successRate = newSuccessCount / metadata.usageCount;
    }

    agent.updatedAt = new Date().toISOString();
    await this.persistAgent(agent);
  }

  // Utility methods
  getAvailableModels(): string[] {
    return Array.from(this.modelCapabilities.keys());
  }

  getModelCapabilities(model: string): string[] {
    return this.modelCapabilities.get(model) || [];
  }

  getAgentTypes(): AgentConfig['type'][] {
    return ['content', 'seo', 'research', 'review', 'analysis', 'custom'];
  }

  private generateAgentId(name: string): string {
    return name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '') + '-' + Date.now();
  }

  // Persistence methods (to be implemented with actual storage)
  private async persistAgent(agent: AgentConfig): Promise<void> {
    console.log('Persisting agent:', agent.id);
  }

  private async removePersistedAgent(id: string): Promise<void> {
    console.log('Removing agent:', id);
  }

  private async persistProfile(profile: AgentProfile): Promise<void> {
    console.log('Persisting profile:', profile.id);
  }

  // Export/Import
  async exportAgent(id: string): Promise<string | null> {
    const agent = this.agents.get(id);
    if (!agent) return null;
    return JSON.stringify(agent, null, 2);
  }

  async importAgent(agentJson: string): Promise<AgentConfig> {
    const agent = JSON.parse(agentJson) as AgentConfig;
    return await this.createAgent(agent);
  }
}

export const agentConfigManager = AgentConfigManager.getInstance();
