/**
 * Configuration Manager
 * Central configuration management system
 */

export interface ConfigSchema {
  id: string;
  name: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  default?: any;
  required?: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
    custom?: (value: any) => boolean | string;
  };
  category?: string;
  sensitive?: boolean;
  restart_required?: boolean;
}

export interface ConfigValue {
  key: string;
  value: any;
  type: string;
  updatedAt: string;
  updatedBy?: string;
  environment?: string;
}

export interface ConfigCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  order?: number;
}

export class ConfigManager {
  private static instance: ConfigManager;
  private configs: Map<string, ConfigValue> = new Map();
  private schemas: Map<string, ConfigSchema> = new Map();
  private categories: Map<string, ConfigCategory> = new Map();
  private listeners: Map<string, ((value: any) => void)[]> = new Map();

  private constructor() {
    this.initializeDefaultCategories();
    this.initializeDefaultSchemas();
  }

  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  private initializeDefaultCategories() {
    const defaultCategories: ConfigCategory[] = [
      {
        id: 'general',
        name: 'General',
        description: 'General application settings',
        icon: '⚙️',
        order: 1
      },
      {
        id: 'workflow',
        name: 'Workflow',
        description: 'Workflow execution settings',
        icon: '🔄',
        order: 2
      },
      {
        id: 'agents',
        name: 'AI Agents',
        description: 'AI agent configuration',
        icon: '🤖',
        order: 3
      },
      {
        id: 'costs',
        name: 'Cost Management',
        description: 'Budget and cost settings',
        icon: '💰',
        order: 4
      },
      {
        id: 'security',
        name: 'Security',
        description: 'Security and authentication settings',
        icon: '🔒',
        order: 5
      },
      {
        id: 'notifications',
        name: 'Notifications',
        description: 'Notification preferences',
        icon: '🔔',
        order: 6
      }
    ];

    defaultCategories.forEach(category => {
      this.categories.set(category.id, category);
    });
  }

  private initializeDefaultSchemas() {
    const defaultSchemas: ConfigSchema[] = [
      // General Settings
      {
        id: 'app.name',
        name: 'Application Name',
        description: 'The name of the application',
        type: 'string',
        default: 'AuthenCIO CMS',
        category: 'general'
      },
      {
        id: 'app.debug',
        name: 'Debug Mode',
        description: 'Enable debug logging and features',
        type: 'boolean',
        default: false,
        category: 'general',
        restart_required: true
      },
      {
        id: 'app.max_concurrent_workflows',
        name: 'Max Concurrent Workflows',
        description: 'Maximum number of workflows that can run simultaneously',
        type: 'number',
        default: 5,
        validation: { min: 1, max: 20 },
        category: 'workflow'
      },

      // Workflow Settings
      {
        id: 'workflow.default_timeout',
        name: 'Default Timeout',
        description: 'Default timeout for workflow steps (in seconds)',
        type: 'number',
        default: 300,
        validation: { min: 30, max: 3600 },
        category: 'workflow'
      },
      {
        id: 'workflow.auto_retry',
        name: 'Auto Retry Failed Steps',
        description: 'Automatically retry failed steps',
        type: 'boolean',
        default: true,
        category: 'workflow'
      },
      {
        id: 'workflow.max_retries',
        name: 'Max Retries',
        description: 'Maximum number of retry attempts',
        type: 'number',
        default: 3,
        validation: { min: 0, max: 10 },
        category: 'workflow'
      },

      // Agent Settings
      {
        id: 'agents.default_model',
        name: 'Default AI Model',
        description: 'Default AI model for agents',
        type: 'string',
        default: 'gpt-4',
        validation: {
          enum: ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'claude-3-sonnet', 'claude-3-opus']
        },
        category: 'agents'
      },
      {
        id: 'agents.max_tokens',
        name: 'Max Tokens',
        description: 'Maximum tokens per agent request',
        type: 'number',
        default: 4000,
        validation: { min: 100, max: 32000 },
        category: 'agents'
      },
      {
        id: 'agents.temperature',
        name: 'Temperature',
        description: 'Default temperature for AI responses',
        type: 'number',
        default: 0.7,
        validation: { min: 0, max: 2 },
        category: 'agents'
      },

      // Cost Management
      {
        id: 'costs.default_budget',
        name: 'Default Budget',
        description: 'Default budget limit for workflows (USD)',
        type: 'number',
        default: 10.0,
        validation: { min: 0.01, max: 1000 },
        category: 'costs'
      },
      {
        id: 'costs.alert_threshold',
        name: 'Budget Alert Threshold',
        description: 'Budget usage percentage to trigger alerts',
        type: 'number',
        default: 0.8,
        validation: { min: 0.1, max: 1.0 },
        category: 'costs'
      },
      {
        id: 'costs.track_usage',
        name: 'Track Usage',
        description: 'Enable detailed cost tracking',
        type: 'boolean',
        default: true,
        category: 'costs'
      },

      // Security Settings
      {
        id: 'security.api_key_rotation_days',
        name: 'API Key Rotation',
        description: 'Days between API key rotation',
        type: 'number',
        default: 90,
        validation: { min: 7, max: 365 },
        category: 'security'
      },
      {
        id: 'security.require_approval',
        name: 'Require Approval',
        description: 'Require approval for sensitive operations',
        type: 'boolean',
        default: true,
        category: 'security'
      },

      // Notification Settings
      {
        id: 'notifications.email_enabled',
        name: 'Email Notifications',
        description: 'Enable email notifications',
        type: 'boolean',
        default: true,
        category: 'notifications'
      },
      {
        id: 'notifications.workflow_completion',
        name: 'Workflow Completion Notifications',
        description: 'Send notifications when workflows complete',
        type: 'boolean',
        default: true,
        category: 'notifications'
      },
      {
        id: 'notifications.error_alerts',
        name: 'Error Alerts',
        description: 'Send notifications for errors',
        type: 'boolean',
        default: true,
        category: 'notifications'
      }
    ];

    defaultSchemas.forEach(schema => {
      this.schemas.set(schema.id, schema);
    });
  }

  // Configuration CRUD operations
  async get<T = any>(key: string, defaultValue?: T): Promise<T> {
    const config = this.configs.get(key);
    if (config) {
      return config.value as T;
    }

    const schema = this.schemas.get(key);
    if (schema && schema.default !== undefined) {
      return schema.default as T;
    }

    return defaultValue as T;
  }

  async set(key: string, value: any, updatedBy?: string): Promise<void> {
    const schema = this.schemas.get(key);
    if (schema) {
      const validationResult = this.validateValue(value, schema);
      if (validationResult !== true) {
        throw new Error(`Validation failed for ${key}: ${validationResult}`);
      }
    }

    const config: ConfigValue = {
      key,
      value,
      type: typeof value,
      updatedAt: new Date().toISOString(),
      updatedBy,
      environment: process.env.NODE_ENV || 'development'
    };

    this.configs.set(key, config);
    
    // Notify listeners
    const listeners = this.listeners.get(key) || [];
    listeners.forEach(listener => listener(value));

    // Persist to storage
    await this.persistConfig(config);
  }

  async delete(key: string): Promise<void> {
    this.configs.delete(key);
    await this.removePersistedConfig(key);
  }

  async getAll(): Promise<ConfigValue[]> {
    return Array.from(this.configs.values());
  }

  async getAllByCategory(categoryId: string): Promise<ConfigValue[]> {
    const categorySchemas = Array.from(this.schemas.values())
      .filter(schema => schema.category === categoryId);
    
    const configs: ConfigValue[] = [];
    for (const schema of categorySchemas) {
      const value = await this.get(schema.id);
      configs.push({
        key: schema.id,
        value,
        type: schema.type,
        updatedAt: this.configs.get(schema.id)?.updatedAt || new Date().toISOString(),
        updatedBy: this.configs.get(schema.id)?.updatedBy,
        environment: this.configs.get(schema.id)?.environment || 'default'
      });
    }
    
    return configs;
  }

  // Schema management
  getSchema(key: string): ConfigSchema | undefined {
    return this.schemas.get(key);
  }

  getAllSchemas(): ConfigSchema[] {
    return Array.from(this.schemas.values());
  }

  getSchemasByCategory(categoryId: string): ConfigSchema[] {
    return Array.from(this.schemas.values())
      .filter(schema => schema.category === categoryId);
  }

  registerSchema(schema: ConfigSchema): void {
    this.schemas.set(schema.id, schema);
  }

  // Category management
  getCategories(): ConfigCategory[] {
    return Array.from(this.categories.values())
      .sort((a, b) => (a.order || 0) - (b.order || 0));
  }

  getCategory(id: string): ConfigCategory | undefined {
    return this.categories.get(id);
  }

  registerCategory(category: ConfigCategory): void {
    this.categories.set(category.id, category);
  }

  // Validation
  private validateValue(value: any, schema: ConfigSchema): true | string {
    if (schema.required && (value === undefined || value === null)) {
      return 'Value is required';
    }

    if (value === undefined || value === null) {
      return true;
    }

    // Type validation
    if (schema.type === 'number' && typeof value !== 'number') {
      return 'Value must be a number';
    }
    if (schema.type === 'string' && typeof value !== 'string') {
      return 'Value must be a string';
    }
    if (schema.type === 'boolean' && typeof value !== 'boolean') {
      return 'Value must be a boolean';
    }

    // Validation rules
    if (schema.validation) {
      const { min, max, pattern, enum: enumValues, custom } = schema.validation;

      if (min !== undefined && value < min) {
        return `Value must be at least ${min}`;
      }
      if (max !== undefined && value > max) {
        return `Value must be at most ${max}`;
      }
      if (pattern && typeof value === 'string' && !new RegExp(pattern).test(value)) {
        return `Value must match pattern: ${pattern}`;
      }
      if (enumValues && !enumValues.includes(value)) {
        return `Value must be one of: ${enumValues.join(', ')}`;
      }
      if (custom) {
        const result = custom(value);
        if (result !== true) {
          return typeof result === 'string' ? result : 'Custom validation failed';
        }
      }
    }

    return true;
  }

  // Event listeners
  subscribe(key: string, listener: (value: any) => void): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, []);
    }
    this.listeners.get(key)!.push(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(key);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // Persistence (to be implemented with actual storage)
  private async persistConfig(config: ConfigValue): Promise<void> {
    // Implementation would save to database/file system
    console.log('Persisting config:', config);
  }

  private async removePersistedConfig(key: string): Promise<void> {
    // Implementation would remove from database/file system
    console.log('Removing config:', key);
  }

  // Load configurations from storage
  async loadConfigs(): Promise<void> {
    // Implementation would load from database/file system
    console.log('Loading configs from storage');
  }

  // Export/Import
  async exportConfigs(): Promise<string> {
    const configs = await this.getAll();
    return JSON.stringify(configs, null, 2);
  }

  async importConfigs(configsJson: string, updatedBy?: string): Promise<void> {
    const configs = JSON.parse(configsJson) as ConfigValue[];
    
    for (const config of configs) {
      await this.set(config.key, config.value, updatedBy);
    }
  }

  // Reset to defaults
  async resetToDefaults(): Promise<void> {
    this.configs.clear();
    console.log('Reset all configurations to defaults');
  }

  async resetCategoryToDefaults(categoryId: string): Promise<void> {
    const schemas = this.getSchemasByCategory(categoryId);
    for (const schema of schemas) {
      this.configs.delete(schema.id);
    }
    console.log(`Reset category ${categoryId} to defaults`);
  }
}

export const configManager = ConfigManager.getInstance();
