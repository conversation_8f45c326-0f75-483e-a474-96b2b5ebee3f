/**
 * Configuration Management System
 * Central exports for all configuration managers
 */

// Configuration Manager
export {
  ConfigManager,
  configManager,
  type ConfigSchema,
  type ConfigValue,
  type ConfigCategory
} from './config-manager';

// Template Manager
export {
  TemplateManager,
  templateManager,
  type WorkflowTemplate,
  type TemplateStep,
  type TemplateVariable,
  type TemplateSettings,
  type TemplateMetadata,
  type TemplateCustomization
} from './template-manager';

// Agent Configuration
export {
  AgentConfigManager,
  agentConfigManager,
  type AgentConfig,
  type AgentSettings,
  type AgentPrompts,
  type PromptExample,
  type AgentConstraints,
  type AgentMetadata,
  type AgentProfile
} from './agent-config';

// User Preferences
export {
  UserPreferencesManager,
  userPreferencesManager,
  type UserPreferences,
  type GeneralPreferences,
  type WorkflowPreferences,
  type UIPreferences,
  type NotificationPreferences,
  type EmailNotifications,
  type BrowserNotifications,
  type SlackNotifications,
  type DiscordNotifications,
  type WebhookNotifications,
  type PrivacyPreferences,
  type IntegrationPreferences,
  type GitHubIntegration,
  type GoogleIntegration,
  type MicrosoftIntegration,
  type ZapierIntegration,
  type CustomIntegration
} from './user-preferences';

// Utility functions for configuration management
export class ConfigurationUtils {
  /**
   * Initialize all configuration managers
   */
  static async initializeAll(): Promise<void> {
    try {
      await configManager.loadConfigs();
      console.log('Configuration managers initialized successfully');
    } catch (error) {
      console.error('Failed to initialize configuration managers:', error);
      throw error;
    }
  }

  /**
   * Export all configurations for backup
   */
  static async exportAllConfigurations(): Promise<{
    configs: string;
    templates: string[];
    agents: string[];
    timestamp: string;
  }> {
    const configs = await configManager.exportConfigs();
    
    const templates = await templateManager.getAllTemplates();
    const templateExports = await Promise.all(
      templates.map(t => templateManager.exportTemplate(t.id))
    );

    const agents = await agentConfigManager.getAllAgents();
    const agentExports = await Promise.all(
      agents.map(a => agentConfigManager.exportAgent(a.id))
    );

    return {
      configs,
      templates: templateExports.filter(Boolean) as string[],
      agents: agentExports.filter(Boolean) as string[],
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Import configurations from backup
   */
  static async importAllConfigurations(backup: {
    configs?: string;
    templates?: string[];
    agents?: string[];
  }): Promise<void> {
    try {
      if (backup.configs) {
        await configManager.importConfigs(backup.configs);
      }

      if (backup.templates) {
        for (const templateJson of backup.templates) {
          await templateManager.importTemplate(templateJson);
        }
      }

      if (backup.agents) {
        for (const agentJson of backup.agents) {
          await agentConfigManager.importAgent(agentJson);
        }
      }

      console.log('All configurations imported successfully');
    } catch (error) {
      console.error('Failed to import configurations:', error);
      throw error;
    }
  }

  /**
   * Reset all configurations to defaults
   */
  static async resetAllToDefaults(): Promise<void> {
    try {
      await configManager.resetToDefaults();
      console.log('All configurations reset to defaults');
    } catch (error) {
      console.error('Failed to reset configurations:', error);
      throw error;
    }
  }

  /**
   * Validate all configurations
   */
  static async validateAllConfigurations(): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Validate templates
      const templates = await templateManager.getAllTemplates();
      for (const template of templates) {
        const validation = templateManager.validateTemplate(template);
        if (!validation.valid) {
          errors.push(`Template ${template.name}: ${validation.errors.join(', ')}`);
        }
      }

      // Validate agents
      const agents = await agentConfigManager.getAllAgents();
      for (const agent of agents) {
        const validation = agentConfigManager.validateAgent(agent);
        if (!validation.valid) {
          errors.push(`Agent ${agent.name}: ${validation.errors.join(', ')}`);
        }
      }

      // Check for orphaned references
      const templateAgentIds = new Set<string>();
      templates.forEach(template => {
        template.steps.forEach(step => {
          if (step.agentId) {
            templateAgentIds.add(step.agentId);
          }
        });
      });

      const availableAgentIds = new Set(agents.map(a => a.id));
      const orphanedAgentRefs = Array.from(templateAgentIds).filter(id => !availableAgentIds.has(id));
      
      if (orphanedAgentRefs.length > 0) {
        warnings.push(`Templates reference non-existent agents: ${orphanedAgentRefs.join(', ')}`);
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { valid: false, errors, warnings };
    }
  }

  /**
   * Get configuration statistics
   */
  static async getConfigurationStats(): Promise<{
    totalConfigs: number;
    totalTemplates: number;
    activeTemplates: number;
    customTemplates: number;
    totalAgents: number;
    activeAgents: number;
    customAgents: number;
    categories: string[];
  }> {
    const configs = await configManager.getAll();
    const templates = await templateManager.getAllTemplates();
    const agents = await agentConfigManager.getAllAgents();
    const categories = configManager.getCategories();

    return {
      totalConfigs: configs.length,
      totalTemplates: templates.length,
      activeTemplates: templates.filter(t => t.isActive).length,
      customTemplates: templates.filter(t => t.isCustom).length,
      totalAgents: agents.length,
      activeAgents: agents.filter(a => a.isActive).length,
      customAgents: agents.filter(a => a.isCustom).length,
      categories: categories.map(c => c.name)
    };
  }

  /**
   * Search configurations
   */
  static async searchConfigurations(query: string): Promise<{
    configs: ConfigValue[];
    templates: WorkflowTemplate[];
    agents: AgentConfig[];
  }> {
    const lowerQuery = query.toLowerCase();

    const configs = (await configManager.getAll()).filter(config =>
      config.key.toLowerCase().includes(lowerQuery) ||
      String(config.value).toLowerCase().includes(lowerQuery)
    );

    const templates = (await templateManager.getAllTemplates()).filter(template =>
      template.name.toLowerCase().includes(lowerQuery) ||
      template.description?.toLowerCase().includes(lowerQuery) ||
      template.tags.some(tag => tag.toLowerCase().includes(lowerQuery))
    );

    const agents = (await agentConfigManager.getAllAgents()).filter(agent =>
      agent.name.toLowerCase().includes(lowerQuery) ||
      agent.description?.toLowerCase().includes(lowerQuery) ||
      agent.capabilities.some(cap => cap.toLowerCase().includes(lowerQuery))
    );

    return { configs, templates, agents };
  }
}

// Default export for convenience
export default {
  configManager,
  templateManager,
  agentConfigManager,
  userPreferencesManager,
  ConfigurationUtils
};
