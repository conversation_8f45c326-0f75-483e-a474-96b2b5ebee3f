/**
 * User Preferences Manager
 * Manages user-specific settings and preferences
 */

export interface UserPreferences {
  userId: string;
  general: GeneralPreferences;
  workflow: WorkflowPreferences;
  ui: UIPreferences;
  notifications: NotificationPreferences;
  privacy: PrivacyPreferences;
  integrations: IntegrationPreferences;
  createdAt: string;
  updatedAt: string;
}

export interface GeneralPreferences {
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  currency: string;
  theme: 'light' | 'dark' | 'auto';
  compactMode: boolean;
  autoSave: boolean;
  autoSaveInterval: number; // seconds
}

export interface WorkflowPreferences {
  defaultTemplate?: string;
  autoStart: boolean;
  requireConfirmation: boolean;
  showAdvancedOptions: boolean;
  defaultBudget: number;
  defaultTimeout: number;
  maxConcurrentWorkflows: number;
  favoriteAgents: string[];
  recentTemplates: string[];
  customVariables: Record<string, any>;
}

export interface UIPreferences {
  sidebarCollapsed: boolean;
  showMinimap: boolean;
  showLineNumbers: boolean;
  fontSize: number;
  fontFamily: string;
  tabSize: number;
  wordWrap: boolean;
  showWhitespace: boolean;
  highlightCurrentLine: boolean;
  showProgressAnimations: boolean;
  compactTables: boolean;
  itemsPerPage: number;
}

export interface NotificationPreferences {
  email: EmailNotifications;
  browser: BrowserNotifications;
  slack?: SlackNotifications;
  discord?: DiscordNotifications;
  webhook?: WebhookNotifications;
}

export interface EmailNotifications {
  enabled: boolean;
  workflowStart: boolean;
  workflowComplete: boolean;
  workflowFailed: boolean;
  budgetAlert: boolean;
  weeklyReport: boolean;
  monthlyReport: boolean;
  frequency: 'immediate' | 'hourly' | 'daily';
}

export interface BrowserNotifications {
  enabled: boolean;
  workflowComplete: boolean;
  workflowFailed: boolean;
  budgetAlert: boolean;
  sound: boolean;
  vibration: boolean;
}

export interface SlackNotifications {
  enabled: boolean;
  webhookUrl: string;
  channel?: string;
  workflowComplete: boolean;
  workflowFailed: boolean;
  budgetAlert: boolean;
}

export interface DiscordNotifications {
  enabled: boolean;
  webhookUrl: string;
  workflowComplete: boolean;
  workflowFailed: boolean;
  budgetAlert: boolean;
}

export interface WebhookNotifications {
  enabled: boolean;
  url: string;
  secret?: string;
  events: string[];
  headers?: Record<string, string>;
}

export interface PrivacyPreferences {
  shareUsageData: boolean;
  shareErrorReports: boolean;
  allowAnalytics: boolean;
  dataRetention: number; // days
  exportFormat: 'json' | 'csv' | 'xml';
  deleteAccountData: boolean;
}

export interface IntegrationPreferences {
  github?: GitHubIntegration;
  google?: GoogleIntegration;
  microsoft?: MicrosoftIntegration;
  zapier?: ZapierIntegration;
  custom?: CustomIntegration[];
}

export interface GitHubIntegration {
  enabled: boolean;
  token?: string;
  defaultRepo?: string;
  autoCommit: boolean;
  branchPrefix: string;
}

export interface GoogleIntegration {
  enabled: boolean;
  driveSync: boolean;
  sheetsExport: boolean;
  calendarIntegration: boolean;
}

export interface MicrosoftIntegration {
  enabled: boolean;
  oneDriveSync: boolean;
  teamsNotifications: boolean;
  outlookCalendar: boolean;
}

export interface ZapierIntegration {
  enabled: boolean;
  webhookUrl?: string;
  triggers: string[];
}

export interface CustomIntegration {
  id: string;
  name: string;
  enabled: boolean;
  config: Record<string, any>;
}

export class UserPreferencesManager {
  private static instance: UserPreferencesManager;
  private preferences: Map<string, UserPreferences> = new Map();
  private defaultPreferences: Omit<UserPreferences, 'userId' | 'createdAt' | 'updatedAt'>;

  private constructor() {
    this.defaultPreferences = this.createDefaultPreferences();
  }

  static getInstance(): UserPreferencesManager {
    if (!UserPreferencesManager.instance) {
      UserPreferencesManager.instance = new UserPreferencesManager();
    }
    return UserPreferencesManager.instance;
  }

  private createDefaultPreferences(): Omit<UserPreferences, 'userId' | 'createdAt' | 'updatedAt'> {
    return {
      general: {
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24h',
        currency: 'USD',
        theme: 'auto',
        compactMode: false,
        autoSave: true,
        autoSaveInterval: 30
      },
      workflow: {
        autoStart: false,
        requireConfirmation: true,
        showAdvancedOptions: false,
        defaultBudget: 10.0,
        defaultTimeout: 300,
        maxConcurrentWorkflows: 3,
        favoriteAgents: [],
        recentTemplates: [],
        customVariables: {}
      },
      ui: {
        sidebarCollapsed: false,
        showMinimap: true,
        showLineNumbers: true,
        fontSize: 14,
        fontFamily: 'Monaco, Consolas, monospace',
        tabSize: 2,
        wordWrap: true,
        showWhitespace: false,
        highlightCurrentLine: true,
        showProgressAnimations: true,
        compactTables: false,
        itemsPerPage: 25
      },
      notifications: {
        email: {
          enabled: true,
          workflowStart: false,
          workflowComplete: true,
          workflowFailed: true,
          budgetAlert: true,
          weeklyReport: true,
          monthlyReport: false,
          frequency: 'immediate'
        },
        browser: {
          enabled: true,
          workflowComplete: true,
          workflowFailed: true,
          budgetAlert: true,
          sound: true,
          vibration: false
        }
      },
      privacy: {
        shareUsageData: true,
        shareErrorReports: true,
        allowAnalytics: true,
        dataRetention: 90,
        exportFormat: 'json',
        deleteAccountData: false
      },
      integrations: {}
    };
  }

  // Preferences CRUD operations
  async getUserPreferences(userId: string): Promise<UserPreferences> {
    let preferences = this.preferences.get(userId);
    
    if (!preferences) {
      preferences = await this.createUserPreferences(userId);
    }
    
    return preferences;
  }

  async createUserPreferences(userId: string): Promise<UserPreferences> {
    const now = new Date().toISOString();
    const preferences: UserPreferences = {
      userId,
      ...this.defaultPreferences,
      createdAt: now,
      updatedAt: now
    };

    this.preferences.set(userId, preferences);
    await this.persistPreferences(preferences);
    return preferences;
  }

  async updateUserPreferences(
    userId: string, 
    updates: Partial<Omit<UserPreferences, 'userId' | 'createdAt' | 'updatedAt'>>
  ): Promise<UserPreferences> {
    const currentPreferences = await this.getUserPreferences(userId);
    
    const updatedPreferences: UserPreferences = {
      ...currentPreferences,
      ...updates,
      userId, // Ensure userId doesn't change
      updatedAt: new Date().toISOString()
    };

    this.preferences.set(userId, updatedPreferences);
    await this.persistPreferences(updatedPreferences);
    return updatedPreferences;
  }

  async updatePreferenceSection<K extends keyof Omit<UserPreferences, 'userId' | 'createdAt' | 'updatedAt'>>(
    userId: string,
    section: K,
    updates: Partial<UserPreferences[K]>
  ): Promise<UserPreferences> {
    const currentPreferences = await this.getUserPreferences(userId);
    
    const updatedPreferences: UserPreferences = {
      ...currentPreferences,
      [section]: {
        ...currentPreferences[section],
        ...updates
      },
      updatedAt: new Date().toISOString()
    };

    this.preferences.set(userId, updatedPreferences);
    await this.persistPreferences(updatedPreferences);
    return updatedPreferences;
  }

  async deleteUserPreferences(userId: string): Promise<boolean> {
    const preferences = this.preferences.get(userId);
    if (!preferences) return false;

    this.preferences.delete(userId);
    await this.removePersistedPreferences(userId);
    return true;
  }

  // Specific preference getters/setters
  async getGeneralPreferences(userId: string): Promise<GeneralPreferences> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.general;
  }

  async updateGeneralPreferences(userId: string, updates: Partial<GeneralPreferences>): Promise<UserPreferences> {
    return this.updatePreferenceSection(userId, 'general', updates);
  }

  async getWorkflowPreferences(userId: string): Promise<WorkflowPreferences> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.workflow;
  }

  async updateWorkflowPreferences(userId: string, updates: Partial<WorkflowPreferences>): Promise<UserPreferences> {
    return this.updatePreferenceSection(userId, 'workflow', updates);
  }

  async getUIPreferences(userId: string): Promise<UIPreferences> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.ui;
  }

  async updateUIPreferences(userId: string, updates: Partial<UIPreferences>): Promise<UserPreferences> {
    return this.updatePreferenceSection(userId, 'ui', updates);
  }

  async getNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    const preferences = await this.getUserPreferences(userId);
    return preferences.notifications;
  }

  async updateNotificationPreferences(userId: string, updates: Partial<NotificationPreferences>): Promise<UserPreferences> {
    return this.updatePreferenceSection(userId, 'notifications', updates);
  }

  // Utility methods
  async addFavoriteAgent(userId: string, agentId: string): Promise<void> {
    const workflowPrefs = await this.getWorkflowPreferences(userId);
    if (!workflowPrefs.favoriteAgents.includes(agentId)) {
      workflowPrefs.favoriteAgents.push(agentId);
      await this.updateWorkflowPreferences(userId, { favoriteAgents: workflowPrefs.favoriteAgents });
    }
  }

  async removeFavoriteAgent(userId: string, agentId: string): Promise<void> {
    const workflowPrefs = await this.getWorkflowPreferences(userId);
    const index = workflowPrefs.favoriteAgents.indexOf(agentId);
    if (index > -1) {
      workflowPrefs.favoriteAgents.splice(index, 1);
      await this.updateWorkflowPreferences(userId, { favoriteAgents: workflowPrefs.favoriteAgents });
    }
  }

  async addRecentTemplate(userId: string, templateId: string): Promise<void> {
    const workflowPrefs = await this.getWorkflowPreferences(userId);
    const recentTemplates = workflowPrefs.recentTemplates.filter(id => id !== templateId);
    recentTemplates.unshift(templateId);
    
    // Keep only the 10 most recent
    const updatedRecent = recentTemplates.slice(0, 10);
    await this.updateWorkflowPreferences(userId, { recentTemplates: updatedRecent });
  }

  async setCustomVariable(userId: string, key: string, value: any): Promise<void> {
    const workflowPrefs = await this.getWorkflowPreferences(userId);
    const customVariables = { ...workflowPrefs.customVariables, [key]: value };
    await this.updateWorkflowPreferences(userId, { customVariables });
  }

  async removeCustomVariable(userId: string, key: string): Promise<void> {
    const workflowPrefs = await this.getWorkflowPreferences(userId);
    const customVariables = { ...workflowPrefs.customVariables };
    delete customVariables[key];
    await this.updateWorkflowPreferences(userId, { customVariables });
  }

  // Validation
  validatePreferences(preferences: Partial<UserPreferences>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // General preferences validation
    if (preferences.general) {
      const { fontSize, autoSaveInterval } = preferences.general;
      if (fontSize && (fontSize < 8 || fontSize > 32)) {
        errors.push('Font size must be between 8 and 32');
      }
      if (autoSaveInterval && (autoSaveInterval < 5 || autoSaveInterval > 300)) {
        errors.push('Auto-save interval must be between 5 and 300 seconds');
      }
    }

    // Workflow preferences validation
    if (preferences.workflow) {
      const { defaultBudget, defaultTimeout, maxConcurrentWorkflows } = preferences.workflow;
      if (defaultBudget && (defaultBudget < 0.01 || defaultBudget > 1000)) {
        errors.push('Default budget must be between $0.01 and $1000');
      }
      if (defaultTimeout && (defaultTimeout < 30 || defaultTimeout > 3600)) {
        errors.push('Default timeout must be between 30 and 3600 seconds');
      }
      if (maxConcurrentWorkflows && (maxConcurrentWorkflows < 1 || maxConcurrentWorkflows > 20)) {
        errors.push('Max concurrent workflows must be between 1 and 20');
      }
    }

    // UI preferences validation
    if (preferences.ui) {
      const { itemsPerPage, tabSize } = preferences.ui;
      if (itemsPerPage && (itemsPerPage < 5 || itemsPerPage > 100)) {
        errors.push('Items per page must be between 5 and 100');
      }
      if (tabSize && (tabSize < 1 || tabSize > 8)) {
        errors.push('Tab size must be between 1 and 8');
      }
    }

    return { valid: errors.length === 0, errors };
  }

  // Export/Import
  async exportUserPreferences(userId: string): Promise<string> {
    const preferences = await this.getUserPreferences(userId);
    return JSON.stringify(preferences, null, 2);
  }

  async importUserPreferences(userId: string, preferencesJson: string): Promise<UserPreferences> {
    const importedPreferences = JSON.parse(preferencesJson) as Partial<UserPreferences>;
    
    const validation = this.validatePreferences(importedPreferences);
    if (!validation.valid) {
      throw new Error(`Invalid preferences: ${validation.errors.join(', ')}`);
    }

    return await this.updateUserPreferences(userId, importedPreferences);
  }

  // Reset preferences
  async resetUserPreferences(userId: string): Promise<UserPreferences> {
    const now = new Date().toISOString();
    const preferences: UserPreferences = {
      userId,
      ...this.defaultPreferences,
      createdAt: this.preferences.get(userId)?.createdAt || now,
      updatedAt: now
    };

    this.preferences.set(userId, preferences);
    await this.persistPreferences(preferences);
    return preferences;
  }

  async resetPreferenceSection<K extends keyof Omit<UserPreferences, 'userId' | 'createdAt' | 'updatedAt'>>(
    userId: string,
    section: K
  ): Promise<UserPreferences> {
    return this.updatePreferenceSection(userId, section, this.defaultPreferences[section]);
  }

  // Persistence methods (to be implemented with actual storage)
  private async persistPreferences(preferences: UserPreferences): Promise<void> {
    console.log('Persisting preferences for user:', preferences.userId);
  }

  private async removePersistedPreferences(userId: string): Promise<void> {
    console.log('Removing preferences for user:', userId);
  }

  // Load preferences from storage
  async loadUserPreferences(userId: string): Promise<void> {
    console.log('Loading preferences for user:', userId);
    // Implementation would load from database/storage
  }
}

export const userPreferencesManager = UserPreferencesManager.getInstance();
