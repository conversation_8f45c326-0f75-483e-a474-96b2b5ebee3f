/**
 * Template Manager
 * Manages workflow templates and customizations
 */

export interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  version: string;
  category: string;
  tags: string[];
  steps: TemplateStep[];
  variables: TemplateVariable[];
  settings: TemplateSettings;
  metadata: TemplateMetadata;
  isCustom: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

export interface TemplateStep {
  id: string;
  name: string;
  description?: string;
  type: string;
  agentId?: string;
  config: Record<string, any>;
  dependencies: string[];
  optional: boolean;
  timeout?: number;
  retryCount?: number;
  condition?: string;
  order: number;
}

export interface TemplateVariable {
  id: string;
  name: string;
  description?: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  default?: any;
  required: boolean;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
  category?: string;
  sensitive?: boolean;
}

export interface TemplateSettings {
  maxConcurrentSteps: number;
  defaultTimeout: number;
  autoRetry: boolean;
  maxRetries: number;
  costBudget?: number;
  requireApproval: boolean;
  notifications: {
    onStart: boolean;
    onComplete: boolean;
    onError: boolean;
    onApprovalNeeded: boolean;
  };
}

export interface TemplateMetadata {
  estimatedDuration?: number;
  estimatedCost?: number;
  complexity: 'low' | 'medium' | 'high';
  usageCount: number;
  successRate: number;
  averageDuration?: number;
  averageCost?: number;
  lastUsed?: string;
}

export interface TemplateCustomization {
  templateId: string;
  userId?: string;
  name: string;
  description?: string;
  customizations: {
    variables: Record<string, any>;
    stepConfigs: Record<string, Record<string, any>>;
    settings: Partial<TemplateSettings>;
    disabledSteps: string[];
    additionalSteps: TemplateStep[];
  };
  createdAt: string;
  updatedAt: string;
}

export class TemplateManager {
  private static instance: TemplateManager;
  private templates: Map<string, WorkflowTemplate> = new Map();
  private customizations: Map<string, TemplateCustomization> = new Map();
  private categories: Set<string> = new Set();

  private constructor() {
    this.initializeDefaultTemplates();
  }

  static getInstance(): TemplateManager {
    if (!TemplateManager.instance) {
      TemplateManager.instance = new TemplateManager();
    }
    return TemplateManager.instance;
  }

  private initializeDefaultTemplates() {
    const defaultTemplate: WorkflowTemplate = {
      id: 'blog-post-seo',
      name: 'SEO Blog Post Creation',
      description: 'Create SEO-optimized blog posts with research, writing, and optimization',
      version: '1.0.0',
      category: 'Content Creation',
      tags: ['blog', 'seo', 'content', 'writing'],
      steps: [
        {
          id: 'keyword-research',
          name: 'Keyword Research',
          description: 'Research relevant keywords for the topic',
          type: 'seo_agent',
          agentId: 'seo-specialist',
          config: {
            maxKeywords: 10,
            includeRelated: true,
            competitorAnalysis: true
          },
          dependencies: [],
          optional: false,
          timeout: 300,
          retryCount: 2,
          order: 1
        },
        {
          id: 'content-outline',
          name: 'Content Outline',
          description: 'Create a detailed content outline',
          type: 'content_agent',
          agentId: 'content-strategist',
          config: {
            sections: 5,
            includeIntro: true,
            includeConclusion: true
          },
          dependencies: ['keyword-research'],
          optional: false,
          timeout: 240,
          retryCount: 2,
          order: 2
        },
        {
          id: 'content-writing',
          name: 'Content Writing',
          description: 'Write the full blog post content',
          type: 'writing_agent',
          agentId: 'content-writer',
          config: {
            minWords: 1000,
            maxWords: 2000,
            tone: 'professional',
            includeImages: true
          },
          dependencies: ['content-outline'],
          optional: false,
          timeout: 600,
          retryCount: 1,
          order: 3
        },
        {
          id: 'seo-optimization',
          name: 'SEO Optimization',
          description: 'Optimize content for search engines',
          type: 'seo_agent',
          agentId: 'seo-specialist',
          config: {
            optimizeTitle: true,
            optimizeMetaDescription: true,
            optimizeHeadings: true,
            keywordDensity: 0.02
          },
          dependencies: ['content-writing'],
          optional: false,
          timeout: 180,
          retryCount: 2,
          order: 4
        },
        {
          id: 'quality-review',
          name: 'Quality Review',
          description: 'Review content quality and accuracy',
          type: 'review_agent',
          agentId: 'content-reviewer',
          config: {
            checkGrammar: true,
            checkFactAccuracy: true,
            checkSEO: true
          },
          dependencies: ['seo-optimization'],
          optional: true,
          timeout: 300,
          retryCount: 1,
          order: 5
        }
      ],
      variables: [
        {
          id: 'topic',
          name: 'Blog Topic',
          description: 'The main topic for the blog post',
          type: 'string',
          required: true,
          category: 'content'
        },
        {
          id: 'target_audience',
          name: 'Target Audience',
          description: 'The intended audience for the content',
          type: 'string',
          default: 'general',
          required: false,
          category: 'content'
        },
        {
          id: 'word_count',
          name: 'Target Word Count',
          description: 'Desired word count for the blog post',
          type: 'number',
          default: 1500,
          validation: { min: 500, max: 5000 },
          required: false,
          category: 'content'
        },
        {
          id: 'include_images',
          name: 'Include Images',
          description: 'Whether to include image suggestions',
          type: 'boolean',
          default: true,
          required: false,
          category: 'content'
        }
      ],
      settings: {
        maxConcurrentSteps: 2,
        defaultTimeout: 300,
        autoRetry: true,
        maxRetries: 2,
        costBudget: 5.0,
        requireApproval: false,
        notifications: {
          onStart: true,
          onComplete: true,
          onError: true,
          onApprovalNeeded: true
        }
      },
      metadata: {
        estimatedDuration: 1800000, // 30 minutes
        estimatedCost: 2.5,
        complexity: 'medium',
        usageCount: 0,
        successRate: 0.95,
        averageDuration: 1650000,
        averageCost: 2.3
      },
      isCustom: false,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.templates.set(defaultTemplate.id, defaultTemplate);
    this.categories.add(defaultTemplate.category);
  }

  // Template CRUD operations
  async getTemplate(id: string): Promise<WorkflowTemplate | null> {
    return this.templates.get(id) || null;
  }

  async getAllTemplates(): Promise<WorkflowTemplate[]> {
    return Array.from(this.templates.values());
  }

  async getTemplatesByCategory(category: string): Promise<WorkflowTemplate[]> {
    return Array.from(this.templates.values())
      .filter(template => template.category === category);
  }

  async getActiveTemplates(): Promise<WorkflowTemplate[]> {
    return Array.from(this.templates.values())
      .filter(template => template.isActive);
  }

  async createTemplate(template: Omit<WorkflowTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowTemplate> {
    const id = this.generateTemplateId(template.name);
    const now = new Date().toISOString();
    
    const newTemplate: WorkflowTemplate = {
      ...template,
      id,
      createdAt: now,
      updatedAt: now,
      isCustom: true,
      metadata: {
        ...template.metadata,
        usageCount: 0,
        successRate: 0
      }
    };

    this.templates.set(id, newTemplate);
    this.categories.add(template.category);
    
    await this.persistTemplate(newTemplate);
    return newTemplate;
  }

  async updateTemplate(id: string, updates: Partial<WorkflowTemplate>): Promise<WorkflowTemplate | null> {
    const template = this.templates.get(id);
    if (!template) return null;

    const updatedTemplate: WorkflowTemplate = {
      ...template,
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString()
    };

    this.templates.set(id, updatedTemplate);
    await this.persistTemplate(updatedTemplate);
    return updatedTemplate;
  }

  async deleteTemplate(id: string): Promise<boolean> {
    const template = this.templates.get(id);
    if (!template || !template.isCustom) {
      return false; // Can't delete built-in templates
    }

    this.templates.delete(id);
    await this.removePersistedTemplate(id);
    return true;
  }

  async duplicateTemplate(id: string, newName: string): Promise<WorkflowTemplate | null> {
    const template = this.templates.get(id);
    if (!template) return null;

    const duplicated = await this.createTemplate({
      ...template,
      name: newName,
      isCustom: true,
      metadata: {
        ...template.metadata,
        usageCount: 0,
        successRate: 0
      }
    });

    return duplicated;
  }

  // Template customization
  async createCustomization(customization: Omit<TemplateCustomization, 'createdAt' | 'updatedAt'>): Promise<TemplateCustomization> {
    const id = this.generateCustomizationId(customization.templateId, customization.userId);
    const now = new Date().toISOString();
    
    const newCustomization: TemplateCustomization = {
      ...customization,
      createdAt: now,
      updatedAt: now
    };

    this.customizations.set(id, newCustomization);
    await this.persistCustomization(newCustomization);
    return newCustomization;
  }

  async getCustomization(templateId: string, userId?: string): Promise<TemplateCustomization | null> {
    const id = this.generateCustomizationId(templateId, userId);
    return this.customizations.get(id) || null;
  }

  async getUserCustomizations(userId: string): Promise<TemplateCustomization[]> {
    return Array.from(this.customizations.values())
      .filter(customization => customization.userId === userId);
  }

  async applyCustomization(templateId: string, customization: TemplateCustomization): Promise<WorkflowTemplate | null> {
    const template = await this.getTemplate(templateId);
    if (!template) return null;

    const customizedTemplate: WorkflowTemplate = {
      ...template,
      name: customization.name || template.name,
      description: customization.description || template.description,
      variables: template.variables.map(variable => ({
        ...variable,
        default: customization.customizations.variables[variable.id] ?? variable.default
      })),
      steps: template.steps
        .filter(step => !customization.customizations.disabledSteps.includes(step.id))
        .map(step => ({
          ...step,
          config: {
            ...step.config,
            ...customization.customizations.stepConfigs[step.id]
          }
        }))
        .concat(customization.customizations.additionalSteps),
      settings: {
        ...template.settings,
        ...customization.customizations.settings
      }
    };

    return customizedTemplate;
  }

  // Template validation
  validateTemplate(template: WorkflowTemplate): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Basic validation
    if (!template.name?.trim()) {
      errors.push('Template name is required');
    }
    if (!template.category?.trim()) {
      errors.push('Template category is required');
    }
    if (!template.steps || template.steps.length === 0) {
      errors.push('Template must have at least one step');
    }

    // Step validation
    const stepIds = new Set<string>();
    template.steps.forEach((step, index) => {
      if (!step.id?.trim()) {
        errors.push(`Step ${index + 1} must have an ID`);
      } else if (stepIds.has(step.id)) {
        errors.push(`Duplicate step ID: ${step.id}`);
      } else {
        stepIds.add(step.id);
      }

      if (!step.name?.trim()) {
        errors.push(`Step ${step.id} must have a name`);
      }
      if (!step.type?.trim()) {
        errors.push(`Step ${step.id} must have a type`);
      }

      // Dependency validation
      step.dependencies.forEach(depId => {
        if (!stepIds.has(depId)) {
          errors.push(`Step ${step.id} depends on non-existent step: ${depId}`);
        }
      });
    });

    // Variable validation
    const variableIds = new Set<string>();
    template.variables.forEach(variable => {
      if (!variable.id?.trim()) {
        errors.push('Variable must have an ID');
      } else if (variableIds.has(variable.id)) {
        errors.push(`Duplicate variable ID: ${variable.id}`);
      } else {
        variableIds.add(variable.id);
      }

      if (!variable.name?.trim()) {
        errors.push(`Variable ${variable.id} must have a name`);
      }
    });

    return { valid: errors.length === 0, errors };
  }

  // Utility methods
  getCategories(): string[] {
    return Array.from(this.categories).sort();
  }

  async updateTemplateMetadata(id: string, metadata: Partial<TemplateMetadata>): Promise<void> {
    const template = this.templates.get(id);
    if (template) {
      template.metadata = { ...template.metadata, ...metadata };
      template.updatedAt = new Date().toISOString();
      await this.persistTemplate(template);
    }
  }

  private generateTemplateId(name: string): string {
    return name.toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '') + '-' + Date.now();
  }

  private generateCustomizationId(templateId: string, userId?: string): string {
    return `${templateId}-${userId || 'default'}`;
  }

  // Persistence methods (to be implemented with actual storage)
  private async persistTemplate(template: WorkflowTemplate): Promise<void> {
    console.log('Persisting template:', template.id);
  }

  private async removePersistedTemplate(id: string): Promise<void> {
    console.log('Removing template:', id);
  }

  private async persistCustomization(customization: TemplateCustomization): Promise<void> {
    console.log('Persisting customization:', customization.templateId);
  }

  // Export/Import
  async exportTemplate(id: string): Promise<string | null> {
    const template = this.templates.get(id);
    if (!template) return null;
    return JSON.stringify(template, null, 2);
  }

  async importTemplate(templateJson: string): Promise<WorkflowTemplate> {
    const template = JSON.parse(templateJson) as WorkflowTemplate;
    const validation = this.validateTemplate(template);
    
    if (!validation.valid) {
      throw new Error(`Invalid template: ${validation.errors.join(', ')}`);
    }

    return await this.createTemplate(template);
  }
}

export const templateManager = TemplateManager.getInstance();
