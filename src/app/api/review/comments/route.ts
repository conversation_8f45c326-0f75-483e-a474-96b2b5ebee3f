/**
 * Review Comments API
 * GET /api/review/comments - Get comments for a review item
 * POST /api/review/comments - Add new comment
 * PUT /api/review/comments - Update comment (resolve, edit, etc.)
 */

import { NextRequest, NextResponse } from 'next/server';

interface ReviewComment {
  id: string;
  reviewItemId: string;
  content: string;
  author: string;
  authorType: 'human' | 'ai';
  timestamp: string;
  position?: {
    start: number;
    end: number;
  };
  resolved: boolean;
  replies: ReviewComment[];
  parentId?: string;
}

// Mock data store (in production, this would use a database)
let comments: ReviewComment[] = [
  {
    id: 'comment-1',
    reviewItemId: 'review-1',
    content: 'This section needs more specific examples of AI applications in healthcare.',
    author: '<PERSON>',
    authorType: 'human',
    timestamp: '2024-01-15T11:30:00Z',
    position: { start: 0, end: 50 },
    resolved: false,
    replies: []
  },
  {
    id: 'comment-2',
    reviewItemId: 'review-1',
    content: 'Consider adding statistics to support this claim about AI accuracy.',
    author: 'AI Assistant',
    authorType: 'ai',
    timestamp: '2024-01-15T11:45:00Z',
    position: { start: 150, end: 200 },
    resolved: false,
    replies: []
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const reviewItemId = searchParams.get('reviewItemId');
    const resolved = searchParams.get('resolved');

    if (!reviewItemId) {
      return NextResponse.json(
        { error: 'Review item ID is required' },
        { status: 400 }
      );
    }

    let filteredComments = comments.filter(comment => 
      comment.reviewItemId === reviewItemId && !comment.parentId
    );

    // Apply resolved filter if specified
    if (resolved !== null) {
      const isResolved = resolved === 'true';
      filteredComments = filteredComments.filter(comment => comment.resolved === isResolved);
    }

    // Add replies to each comment
    const commentsWithReplies = filteredComments.map(comment => ({
      ...comment,
      replies: comments.filter(reply => reply.parentId === comment.id)
    }));

    // Calculate statistics
    const stats = {
      total: filteredComments.length,
      resolved: filteredComments.filter(c => c.resolved).length,
      unresolved: filteredComments.filter(c => !c.resolved).length,
      aiComments: filteredComments.filter(c => c.authorType === 'ai').length,
      humanComments: filteredComments.filter(c => c.authorType === 'human').length
    };

    return NextResponse.json({
      success: true,
      data: {
        comments: commentsWithReplies,
        stats
      }
    });

  } catch (error) {
    console.error('Comments GET error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch comments',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      reviewItemId, 
      content, 
      author, 
      authorType = 'human',
      position,
      parentId,
      aiAssistanceEnabled = false
    } = body;

    if (!reviewItemId || !content || !author) {
      return NextResponse.json(
        { error: 'Review item ID, content, and author are required' },
        { status: 400 }
      );
    }

    const newComment: ReviewComment = {
      id: `comment-${Date.now()}`,
      reviewItemId,
      content,
      author,
      authorType,
      timestamp: new Date().toISOString(),
      position,
      resolved: false,
      replies: [],
      parentId
    };

    comments.push(newComment);

    // If AI assistance is enabled and this is a human comment, generate AI suggestion
    if (aiAssistanceEnabled && authorType === 'human' && !parentId) {
      setTimeout(async () => {
        const aiSuggestion = await generateAISuggestion(content, position);
        if (aiSuggestion) {
          const aiComment: ReviewComment = {
            id: `comment-ai-${Date.now()}`,
            reviewItemId,
            content: aiSuggestion,
            author: 'AI Assistant',
            authorType: 'ai',
            timestamp: new Date().toISOString(),
            resolved: false,
            replies: [],
            parentId: newComment.id
          };
          comments.push(aiComment);
        }
      }, 1000);
    }

    return NextResponse.json({
      success: true,
      data: newComment
    });

  } catch (error) {
    console.error('Comments POST error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create comment',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, updates } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Comment ID is required' },
        { status: 400 }
      );
    }

    const commentIndex = comments.findIndex(comment => comment.id === id);
    if (commentIndex === -1) {
      return NextResponse.json(
        { error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Update the comment
    comments[commentIndex] = {
      ...comments[commentIndex],
      ...updates
    };

    return NextResponse.json({
      success: true,
      data: comments[commentIndex]
    });

  } catch (error) {
    console.error('Comments PUT error:', error);
    return NextResponse.json(
      {
        error: 'Failed to update comment',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Comment ID is required' },
        { status: 400 }
      );
    }

    const commentIndex = comments.findIndex(comment => comment.id === id);
    if (commentIndex === -1) {
      return NextResponse.json(
        { error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Also delete all replies to this comment
    const comment = comments[commentIndex];
    comments = comments.filter(c => c.id !== id && c.parentId !== id);

    return NextResponse.json({
      success: true,
      data: comment
    });

  } catch (error) {
    console.error('Comments DELETE error:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete comment',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

/**
 * Generate AI suggestion based on human comment
 */
async function generateAISuggestion(humanComment: string, position?: {start: number; end: number}): Promise<string | null> {
  // Simulate AI processing time
  await new Promise(resolve => setTimeout(resolve, 500));

  // Simple AI suggestion logic (in production, this would use actual AI)
  const suggestions = {
    'examples': 'Consider adding specific examples like IBM Watson for oncology or Google DeepMind for eye disease detection.',
    'statistics': 'Adding recent statistics from medical journals would strengthen this claim. For example, "Studies show AI can improve diagnostic accuracy by up to 20%."',
    'clarity': 'This sentence could be clearer. Try breaking it into two shorter sentences for better readability.',
    'evidence': 'This claim would benefit from citing recent research or case studies from leading medical institutions.',
    'flow': 'Consider adding a transition sentence to improve the flow between these paragraphs.'
  };

  // Analyze the comment to determine suggestion type
  const comment = humanComment.toLowerCase();
  
  if (comment.includes('example')) {
    return suggestions.examples;
  } else if (comment.includes('statistic') || comment.includes('data')) {
    return suggestions.statistics;
  } else if (comment.includes('clear') || comment.includes('confusing')) {
    return suggestions.clarity;
  } else if (comment.includes('evidence') || comment.includes('support')) {
    return suggestions.evidence;
  } else if (comment.includes('flow') || comment.includes('transition')) {
    return suggestions.flow;
  }

  // Default suggestion
  return 'I suggest reviewing this section for clarity and adding supporting evidence where appropriate.';
}
