/**
 * Review Items API
 * GET /api/review/items - Get review items
 * POST /api/review/items - Create new review item
 */

import { NextRequest, NextResponse } from 'next/server';
import { getStateStore } from '../../../../core/workflow/singleton';

interface ReviewItem {
  id: string;
  title: string;
  content: string;
  originalContent: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'needs_revision';
  reviewerId?: string;
  reviewerName?: string;
  submittedAt: string;
  reviewedAt?: string;
  comments: ReviewComment[];
  changes: ContentChange[];
  qualityScore?: number;
  aiCosts?: {
    totalCost: number;
    assistanceCost: number;
    validationCost: number;
  };
}

interface ReviewComment {
  id: string;
  content: string;
  author: string;
  authorType: 'human' | 'ai';
  timestamp: string;
  position?: {
    start: number;
    end: number;
  };
  resolved: boolean;
  replies: ReviewComment[];
}

interface ContentChange {
  id: string;
  type: 'addition' | 'deletion' | 'modification';
  position: {
    start: number;
    end: number;
  };
  originalText: string;
  newText: string;
  reason: string;
  timestamp: string;
}

// Mock data store (in production, this would use a database)
let reviewItems: ReviewItem[] = [
  {
    id: 'review-1',
    title: 'Blog Post: AI in Healthcare',
    content: 'Artificial intelligence is revolutionizing healthcare by enabling faster diagnosis, personalized treatment plans, and improved patient outcomes. Machine learning algorithms can analyze medical images with remarkable accuracy, often detecting conditions that human doctors might miss.',
    originalContent: 'AI is changing healthcare. It helps doctors diagnose diseases faster and create better treatment plans for patients.',
    status: 'in_review',
    reviewerId: 'reviewer-1',
    reviewerName: 'Sarah Johnson',
    submittedAt: '2024-01-15T10:00:00Z',
    comments: [
      {
        id: 'comment-1',
        content: 'This section needs more specific examples of AI applications in healthcare.',
        author: 'Sarah Johnson',
        authorType: 'human',
        timestamp: '2024-01-15T11:30:00Z',
        position: { start: 0, end: 50 },
        resolved: false,
        replies: []
      }
    ],
    changes: [
      {
        id: 'change-1',
        type: 'modification',
        position: { start: 0, end: 30 },
        originalText: 'AI is changing healthcare.',
        newText: 'Artificial intelligence is revolutionizing healthcare',
        reason: 'More descriptive and professional language',
        timestamp: '2024-01-15T11:00:00Z'
      }
    ],
    qualityScore: 85,
    aiCosts: {
      totalCost: 0.045,
      assistanceCost: 0.025,
      validationCost: 0.020
    }
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const reviewerId = searchParams.get('reviewerId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    let filteredItems = [...reviewItems];

    // Apply filters
    if (status) {
      filteredItems = filteredItems.filter(item => item.status === status);
    }

    if (reviewerId) {
      filteredItems = filteredItems.filter(item => item.reviewerId === reviewerId);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    // Calculate statistics
    const stats = {
      total: reviewItems.length,
      pending: reviewItems.filter(item => item.status === 'pending').length,
      inReview: reviewItems.filter(item => item.status === 'in_review').length,
      approved: reviewItems.filter(item => item.status === 'approved').length,
      rejected: reviewItems.filter(item => item.status === 'rejected').length,
      needsRevision: reviewItems.filter(item => item.status === 'needs_revision').length
    };

    return NextResponse.json({
      success: true,
      data: {
        items: paginatedItems,
        pagination: {
          page,
          limit,
          total: filteredItems.length,
          totalPages: Math.ceil(filteredItems.length / limit)
        },
        stats
      }
    });

  } catch (error) {
    console.error('Review items GET error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch review items',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, content, originalContent, submitterId, submitterName } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      );
    }

    const newReviewItem: ReviewItem = {
      id: `review-${Date.now()}`,
      title,
      content,
      originalContent: originalContent || content,
      status: 'pending',
      submittedAt: new Date().toISOString(),
      comments: [],
      changes: [],
      aiCosts: {
        totalCost: 0,
        assistanceCost: 0,
        validationCost: 0
      }
    };

    reviewItems.push(newReviewItem);

    return NextResponse.json({
      success: true,
      data: newReviewItem
    });

  } catch (error) {
    console.error('Review items POST error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create review item',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, updates } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Review item ID is required' },
        { status: 400 }
      );
    }

    const itemIndex = reviewItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Review item not found' },
        { status: 404 }
      );
    }

    // Update the item
    reviewItems[itemIndex] = {
      ...reviewItems[itemIndex],
      ...updates,
      // Update timestamp if status changed
      ...(updates.status && updates.status !== reviewItems[itemIndex].status && {
        reviewedAt: new Date().toISOString()
      })
    };

    return NextResponse.json({
      success: true,
      data: reviewItems[itemIndex]
    });

  } catch (error) {
    console.error('Review items PUT error:', error);
    return NextResponse.json(
      {
        error: 'Failed to update review item',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Review item ID is required' },
        { status: 400 }
      );
    }

    const itemIndex = reviewItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Review item not found' },
        { status: 404 }
      );
    }

    const deletedItem = reviewItems.splice(itemIndex, 1)[0];

    return NextResponse.json({
      success: true,
      data: deletedItem
    });

  } catch (error) {
    console.error('Review items DELETE error:', error);
    return NextResponse.json(
      {
        error: 'Failed to delete review item',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
