/**
 * Review Page
 * 
 * Main page for the enhanced review interface
 */

'use client';

import React, { useState, useEffect } from 'react';
import EnhancedReviewInterface from '../../components/Review/EnhancedReviewInterface';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { 
  FileText, 
  Clock, 
  User,
  RefreshCw,
  ArrowLeft
} from 'lucide-react';

interface ReviewItem {
  id: string;
  title: string;
  content: string;
  originalContent: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected' | 'needs_revision';
  reviewerId?: string;
  reviewerName?: string;
  submittedAt: string;
  reviewedAt?: string;
  comments: Array<{
    id: string;
    content: string;
    author: string;
    authorType: 'human' | 'ai';
    timestamp: string;
    position?: {start: number; end: number};
    resolved: boolean;
    replies: any[];
  }>;
  changes: Array<{
    id: string;
    type: 'addition' | 'deletion' | 'modification';
    position: {start: number; end: number};
    originalText: string;
    newText: string;
    reason: string;
    timestamp: string;
  }>;
  qualityScore?: number;
  aiCosts?: {
    totalCost: number;
    assistanceCost: number;
    validationCost: number;
  };
}

export default function ReviewPage() {
  const [reviewItems, setReviewItems] = useState<ReviewItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<ReviewItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchReviewItems();
  }, []);

  const fetchReviewItems = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/review/items');
      
      if (!response.ok) {
        throw new Error('Failed to fetch review items');
      }
      
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch review items');
      }
      
      setReviewItems(result.data.items);
      
      // Auto-select first item if available
      if (result.data.items.length > 0 && !selectedItem) {
        setSelectedItem(result.data.items[0]);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (status: ReviewItem['status']) => {
    if (!selectedItem) return;
    
    try {
      const response = await fetch('/api/review/items', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedItem.id,
          updates: { status }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update status');
      }

      const result = await response.json();
      
      if (result.success) {
        setSelectedItem(result.data);
        // Update the item in the list
        setReviewItems(items => 
          items.map(item => 
            item.id === selectedItem.id ? result.data : item
          )
        );
      }
      
    } catch (err) {
      console.error('Failed to update status:', err);
      alert('Failed to update status. Please try again.');
    }
  };

  const handleContentChange = (content: string) => {
    if (!selectedItem) return;
    
    setSelectedItem({
      ...selectedItem,
      content
    });
  };

  const handleCommentAdd = async (comment: any) => {
    if (!selectedItem) return;
    
    try {
      const response = await fetch('/api/review/comments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewItemId: selectedItem.id,
          content: comment.content,
          author: comment.author,
          authorType: comment.authorType,
          position: comment.position,
          aiAssistanceEnabled: true
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to add comment');
      }

      const result = await response.json();
      
      if (result.success) {
        // Add the new comment to the selected item
        setSelectedItem({
          ...selectedItem,
          comments: [...selectedItem.comments, result.data]
        });
      }
      
    } catch (err) {
      console.error('Failed to add comment:', err);
      alert('Failed to add comment. Please try again.');
    }
  };

  const handleSave = async () => {
    if (!selectedItem) return;
    
    try {
      const response = await fetch('/api/review/items', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: selectedItem.id,
          updates: {
            content: selectedItem.content,
            // Add any other updates
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save changes');
      }

      alert('Changes saved successfully!');
      
    } catch (err) {
      console.error('Failed to save:', err);
      alert('Failed to save changes. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600';
      case 'rejected': return 'text-red-600';
      case 'needs_revision': return 'text-orange-600';
      case 'in_review': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved': return 'default';
      case 'rejected': return 'destructive';
      case 'needs_revision': return 'secondary';
      case 'in_review': return 'default';
      default: return 'outline';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2 text-lg">Loading review items...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <Alert variant="destructive">
          <AlertDescription>
            Failed to load review items: {error}
          </AlertDescription>
        </Alert>
        <Button onClick={fetchReviewItems} className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto p-6">
        {!selectedItem ? (
          /* Review Items List */
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Review Dashboard</h1>
                <p className="text-gray-600 mt-1">Manage content reviews and approvals</p>
              </div>
              
              <Button onClick={fetchReviewItems}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {reviewItems.map((item) => (
                <Card key={item.id} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="flex items-center">
                          <FileText className="h-5 w-5 mr-2" />
                          {item.title}
                        </CardTitle>
                        <CardDescription className="mt-2">
                          <div className="flex items-center space-x-4">
                            <span className="flex items-center">
                              <Clock className="h-4 w-4 mr-1" />
                              {new Date(item.submittedAt).toLocaleDateString()}
                            </span>
                            {item.reviewerName && (
                              <span className="flex items-center">
                                <User className="h-4 w-4 mr-1" />
                                {item.reviewerName}
                              </span>
                            )}
                          </div>
                        </CardDescription>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Badge variant={getStatusBadge(item.status)}>
                          {item.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                        {item.qualityScore && (
                          <Badge variant="outline">
                            {item.qualityScore}/100
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-gray-700 mb-4 line-clamp-3">
                      {item.content.substring(0, 200)}...
                    </p>
                    
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span>{item.comments.length} comments</span>
                        <span>{item.changes.length} changes</span>
                        {item.aiCosts && (
                          <span>${item.aiCosts.totalCost.toFixed(3)} AI cost</span>
                        )}
                      </div>
                      
                      <Button onClick={() => setSelectedItem(item)}>
                        Review
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {reviewItems.length === 0 && (
                <Card>
                  <CardContent className="text-center py-12">
                    <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No review items</h3>
                    <p className="text-gray-600">There are no items pending review at this time.</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        ) : (
          /* Enhanced Review Interface */
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => setSelectedItem(null)}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to List
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Review: {selectedItem.title}</h1>
                <p className="text-gray-600">Enhanced review interface with AI assistance</p>
              </div>
            </div>

            <EnhancedReviewInterface
              reviewItem={selectedItem}
              onStatusChange={handleStatusChange}
              onContentChange={handleContentChange}
              onCommentAdd={handleCommentAdd}
              onSave={handleSave}
            />
          </div>
        )}
      </div>
    </div>
  );
}
